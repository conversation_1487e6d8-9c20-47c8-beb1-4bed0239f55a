package com.example.meals.dto;

import com.example.meals.entity.MealRecord;
import com.example.meals.entity.MealItem;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 膳食记录响应DTO
 * {{ AURA-X: Add - 创建膳食记录响应DTO. Approval: 寸止. }}
 */
public class MealRecordResponse {
    
    private Long id;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate recordDate;
    private String mealType;
    private String mealTypeName;
    private BigDecimal totalEnergy;
    private BigDecimal totalProtein;
    private BigDecimal totalFat;
    private BigDecimal totalCarbohydrate;
    private BigDecimal totalDietaryFiber;
    private String notes;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    private List<MealItemResponse> mealItems;
    
    // 膳食条目响应DTO
    public static class MealItemResponse {
        private Long id;
        private Long foodId;
        private String foodName;
        private String foodCode;
        private BigDecimal weight;
        private BigDecimal energy;
        private BigDecimal protein;
        private BigDecimal fat;
        private BigDecimal carbohydrate;
        private BigDecimal dietaryFiber;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createTime;
        
        public MealItemResponse() {}
        
        public MealItemResponse(MealItem mealItem) {
            this.id = mealItem.getId();
            this.foodId = mealItem.getFoodId();
            this.foodName = mealItem.getFoodName();
            this.foodCode = mealItem.getFoodCode();
            this.weight = mealItem.getWeight();
            this.energy = mealItem.getEnergy();
            this.protein = mealItem.getProtein();
            this.fat = mealItem.getFat();
            this.carbohydrate = mealItem.getCarbohydrate();
            this.dietaryFiber = mealItem.getDietaryFiber();
            this.createTime = mealItem.getCreateTime();
        }
        
        // Getter 和 Setter 方法
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public Long getFoodId() { return foodId; }
        public void setFoodId(Long foodId) { this.foodId = foodId; }
        
        public String getFoodName() { return foodName; }
        public void setFoodName(String foodName) { this.foodName = foodName; }
        
        public String getFoodCode() { return foodCode; }
        public void setFoodCode(String foodCode) { this.foodCode = foodCode; }
        
        public BigDecimal getWeight() { return weight; }
        public void setWeight(BigDecimal weight) { this.weight = weight; }
        
        public BigDecimal getEnergy() { return energy; }
        public void setEnergy(BigDecimal energy) { this.energy = energy; }
        
        public BigDecimal getProtein() { return protein; }
        public void setProtein(BigDecimal protein) { this.protein = protein; }
        
        public BigDecimal getFat() { return fat; }
        public void setFat(BigDecimal fat) { this.fat = fat; }
        
        public BigDecimal getCarbohydrate() { return carbohydrate; }
        public void setCarbohydrate(BigDecimal carbohydrate) { this.carbohydrate = carbohydrate; }
        
        public BigDecimal getDietaryFiber() { return dietaryFiber; }
        public void setDietaryFiber(BigDecimal dietaryFiber) { this.dietaryFiber = dietaryFiber; }
        
        public LocalDateTime getCreateTime() { return createTime; }
        public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
    }
    
    // 构造函数
    public MealRecordResponse() {}
    
    public MealRecordResponse(MealRecord mealRecord) {
        this.id = mealRecord.getId();
        this.recordDate = mealRecord.getRecordDate();
        this.mealType = mealRecord.getMealType();
        this.mealTypeName = getMealTypeDisplayName(mealRecord.getMealType());
        this.totalEnergy = mealRecord.getTotalEnergy();
        this.totalProtein = mealRecord.getTotalProtein();
        this.totalFat = mealRecord.getTotalFat();
        this.totalCarbohydrate = mealRecord.getTotalCarbohydrate();
        this.totalDietaryFiber = mealRecord.getTotalDietaryFiber();
        this.notes = mealRecord.getNotes();
        this.createTime = mealRecord.getCreateTime();
        this.updateTime = mealRecord.getUpdateTime();
        
        if (mealRecord.getMealItems() != null) {
            this.mealItems = mealRecord.getMealItems().stream()
                    .map(MealItemResponse::new)
                    .collect(Collectors.toList());
        }
    }
    
    /**
     * 获取膳食类型显示名称
     */
    private String getMealTypeDisplayName(String mealType) {
        if (mealType == null) return "";
        switch (mealType) {
            case "breakfast": return "早餐";
            case "lunch": return "午餐";
            case "dinner": return "晚餐";
            case "snack": return "加餐";
            default: return mealType;
        }
    }
    
    // Getter 和 Setter 方法
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public LocalDate getRecordDate() { return recordDate; }
    public void setRecordDate(LocalDate recordDate) { this.recordDate = recordDate; }
    
    public String getMealType() { return mealType; }
    public void setMealType(String mealType) { this.mealType = mealType; }
    
    public String getMealTypeName() { return mealTypeName; }
    public void setMealTypeName(String mealTypeName) { this.mealTypeName = mealTypeName; }
    
    public BigDecimal getTotalEnergy() { return totalEnergy; }
    public void setTotalEnergy(BigDecimal totalEnergy) { this.totalEnergy = totalEnergy; }
    
    public BigDecimal getTotalProtein() { return totalProtein; }
    public void setTotalProtein(BigDecimal totalProtein) { this.totalProtein = totalProtein; }
    
    public BigDecimal getTotalFat() { return totalFat; }
    public void setTotalFat(BigDecimal totalFat) { this.totalFat = totalFat; }
    
    public BigDecimal getTotalCarbohydrate() { return totalCarbohydrate; }
    public void setTotalCarbohydrate(BigDecimal totalCarbohydrate) { this.totalCarbohydrate = totalCarbohydrate; }
    
    public BigDecimal getTotalDietaryFiber() { return totalDietaryFiber; }
    public void setTotalDietaryFiber(BigDecimal totalDietaryFiber) { this.totalDietaryFiber = totalDietaryFiber; }
    
    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }
    
    public LocalDateTime getCreateTime() { return createTime; }
    public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
    
    public LocalDateTime getUpdateTime() { return updateTime; }
    public void setUpdateTime(LocalDateTime updateTime) { this.updateTime = updateTime; }
    
    public List<MealItemResponse> getMealItems() { return mealItems; }
    public void setMealItems(List<MealItemResponse> mealItems) { this.mealItems = mealItems; }
}

package com.example.meals.dto.request;

/**
 * 创建用户健康目标请求DTO
 */
public class CreateUserGoalRequest {
    
    private String goalCode;
    private String goalType;
    private Double targetValue;
    private String unit;
    private String deadline;
    private String description;
    
    // 构造函数
    public CreateUserGoalRequest() {}
    
    // Getter 和 Setter 方法
    public String getGoalCode() {
        return goalCode;
    }
    
    public void setGoalCode(String goalCode) {
        this.goalCode = goalCode;
    }
    
    public String getGoalType() {
        return goalType;
    }
    
    public void setGoalType(String goalType) {
        this.goalType = goalType;
    }
    
    public Double getTargetValue() {
        return targetValue;
    }
    
    public void setTargetValue(Double targetValue) {
        this.targetValue = targetValue;
    }
    
    public String getUnit() {
        return unit;
    }
    
    public void setUnit(String unit) {
        this.unit = unit;
    }
    
    public String getDeadline() {
        return deadline;
    }
    
    public void setDeadline(String deadline) {
        this.deadline = deadline;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    @Override
    public String toString() {
        return "CreateUserGoalRequest{" +
                "goalCode='" + goalCode + '\'' +
                ", goalType='" + goalType + '\'' +
                ", targetValue=" + targetValue +
                ", unit='" + unit + '\'' +
                ", deadline='" + deadline + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
}

package com.example.meals.controller;

import com.example.meals.common.Result;
import com.example.meals.dto.FoodSearchResponse;
import com.example.meals.dto.NutritionResponse;
import com.example.meals.service.FoodService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 食物控制器
 */
@RestController
@RequestMapping("/api/foods")
public class FoodController {
    
    @Autowired
    private FoodService foodService;
    
    /**
     * 搜索食物
     * @param keyword 搜索关键词
     * @param limit 限制返回数量，默认10
     * @return 食物搜索结果
     */
    @GetMapping("/search")
    public Result<List<FoodSearchResponse>> searchFoods(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "10") Integer limit) {
        return foodService.searchFoods(keyword, limit);
    }
    
    /**
     * 获取所有食物分类
     * @return 分类列表
     */
    @GetMapping("/categories")
    public Result<List<String>> getAllCategories() {
        return foodService.getAllCategories();
    }
    
    /**
     * 根据分类获取食物列表
     * @param categoryCode 分类代码
     * @param limit 限制返回数量，默认20
     * @return 食物列表
     */
    @GetMapping("/category/{categoryCode}")
    public Result<List<FoodSearchResponse>> getFoodsByCategory(
            @PathVariable String categoryCode,
            @RequestParam(defaultValue = "20") Integer limit) {
        return foodService.getFoodsByCategory(categoryCode, limit);
    }
    
    /**
     * 获取热门食物
     * @param limit 限制返回数量，默认6
     * @return 热门食物列表
     */
    @GetMapping("/popular")
    public Result<List<FoodSearchResponse>> getPopularFoods(
            @RequestParam(defaultValue = "6") Integer limit) {
        return foodService.getPopularFoods(limit);
    }
    
    /**
     * 分析食物营养成分
     * @param foodId 食物ID
     * @param weight 重量(克)，默认100克
     * @return 营养分析结果
     */
    @GetMapping("/{foodId}/nutrition")
    public Result<NutritionResponse> analyzeNutrition(
            @PathVariable Long foodId,
            @RequestParam(defaultValue = "100") Integer weight) {
        return foodService.analyzeNutrition(foodId, weight);
    }
    
    /**
     * 根据ID获取食物详情
     * @param foodId 食物ID
     * @return 食物详情
     */
    @GetMapping("/{foodId}")
    public Result<FoodSearchResponse> getFoodById(@PathVariable Long foodId) {
        return foodService.getFoodById(foodId);
    }
}

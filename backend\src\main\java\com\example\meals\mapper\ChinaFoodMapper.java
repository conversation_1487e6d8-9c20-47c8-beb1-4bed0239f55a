package com.example.meals.mapper;

import com.example.meals.entity.ChinaFood;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 中国食物成分表Mapper接口
 */
@Mapper
public interface ChinaFoodMapper {

    /**
     * 根据食物名称搜索食物（模糊匹配）
     * @param keyword 搜索关键词
     * @param limit 限制返回数量
     * @return 食物列表
     */
    List<ChinaFood> searchByName(@Param("keyword") String keyword, @Param("limit") Integer limit);

    /**
     * 获取所有食物分类
     * @return 分类代码列表
     */
    List<String> getAllCategories();

    /**
     * 根据分类获取食物列表
     * @param categoryCode 分类代码
     * @param limit 限制返回数量
     * @return 食物列表
     */
    List<ChinaFood> getFoodsByCategory(@Param("categoryCode") String categoryCode, @Param("limit") Integer limit);

    /**
     * 获取热门食物（按能量值排序，选择常见食物）
     * @param limit 限制返回数量
     * @return 食物列表
     */
    List<ChinaFood> getPopularFoods(@Param("limit") Integer limit);

    /**
     * 根据ID获取食物详情
     * @param id 食物ID
     * @return 食物详情
     */
    ChinaFood getFoodById(@Param("id") Long id);
}

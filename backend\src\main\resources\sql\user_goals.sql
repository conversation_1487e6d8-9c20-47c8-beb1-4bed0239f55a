-- 用户健康目标表
CREATE TABLE IF NOT EXISTS user_goals (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    goal_code VARCHAR(50) NOT NULL COMMENT '目标代码',
    goal_name VARCHAR(100) NOT NULL COMMENT '目标名称',
    goal_type VARCHAR(20) NOT NULL COMMENT '目标类型(calories/weight/exercise/nutrition/water/sleep)',
    target_value DECIMAL(10,2) NOT NULL COMMENT '目标值',
    current_value DECIMAL(10,2) DEFAULT 0.00 COMMENT '当前值',
    unit VARCHAR(20) NOT NULL COMMENT '单位',
    deadline DATE NOT NULL COMMENT '截止日期',
    description TEXT COMMENT '目标描述',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否活跃(1:是 0:否)',
    is_completed TINYINT(1) DEFAULT 0 COMMENT '是否完成(1:是 0:否)',
    progress DECIMAL(5,2) DEFAULT 0.00 COMMENT '完成进度(百分比)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_goal_type (goal_type),
    INDEX idx_is_active (is_active),
    INDEX idx_deadline (deadline),
    
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户健康目标表';

-- 用户健康目标历史记录表
CREATE TABLE IF NOT EXISTS user_goal_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    goal_id BIGINT NOT NULL COMMENT '目标ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    record_date DATE NOT NULL COMMENT '记录日期',
    value DECIMAL(10,2) NOT NULL COMMENT '记录值',
    progress DECIMAL(5,2) NOT NULL COMMENT '当时的进度(百分比)',
    notes TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_goal_id (goal_id),
    INDEX idx_user_id (user_id),
    INDEX idx_record_date (record_date),
    UNIQUE KEY uk_goal_date (goal_id, record_date),
    
    FOREIGN KEY (goal_id) REFERENCES user_goals(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户健康目标历史记录表';

-- 插入一些示例健康目标类型数据（如果health_goals表为空）
INSERT IGNORE INTO health_goals (code, name, description, sort_order, status) VALUES
('calories', '卡路里管理', '控制每日卡路里摄入量', 1, 1),
('weight', '体重管理', '达到理想体重目标', 2, 1),
('exercise', '运动健身', '保持规律的运动习惯', 3, 1),
('nutrition', '营养摄入', '均衡营养素摄入', 4, 1),
('water', '水分补充', '保持充足的水分摄入', 5, 1),
('sleep', '睡眠质量', '改善睡眠时间和质量', 6, 1);

-- 插入一些示例用户目标数据（仅用于测试，实际使用时可删除）
-- INSERT INTO user_goals (user_id, goal_code, goal_name, goal_type, target_value, current_value, unit, deadline, description) VALUES
-- (1, 'calories', '每日卡路里控制', 'calories', 2000.00, 1650.00, 'kcal', '2024-12-31', '控制每日卡路里摄入在2000以内'),
-- (1, 'weight', '目标体重达成', 'weight', 65.00, 68.00, 'kg', '2024-06-30', '减重到65公斤'),
-- (1, 'exercise', '每周运动时长', 'exercise', 300.00, 240.00, '分钟', '2024-12-31', '每周至少运动300分钟');

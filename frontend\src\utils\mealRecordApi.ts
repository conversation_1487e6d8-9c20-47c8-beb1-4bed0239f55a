// 膳食记录相关API接口
// {{ AURA-X: Add - 创建膳食记录前端API工具函数. Approval: 寸止. }}

import { authFetch, apiRequest } from './auth'

// 基础配置
const API_BASE_URL = 'http://localhost:8080/api/meal-records'

// 类型定义
export interface MealItemRequest {
  foodId: number
  weight: number
}

export interface MealRecordRequest {
  recordDate: string // YYYY-MM-DD格式
  mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack'
  notes?: string
  mealItems?: MealItemRequest[]
}

export interface MealItemResponse {
  id: number
  foodId: number
  foodName: string
  foodCode: string
  weight: number
  energy: number
  protein: number
  fat: number
  carbohydrate: number
  dietaryFiber: number
  createTime: string
}

export interface MealRecordResponse {
  id: number
  recordDate: string
  mealType: string
  mealTypeName: string
  totalEnergy: number
  totalProtein: number
  totalFat: number
  totalCarbohydrate: number
  totalDietaryFiber: number
  notes?: string
  createTime: string
  updateTime: string
  mealItems: MealItemResponse[]
}

export interface MealRecordStatistics {
  totalRecords: number
  totalDays: number
  avgDailyEnergy: number
  avgDailyProtein: number
  avgDailyFat: number
  avgDailyCarbohydrate: number
  avgDailyDietaryFiber: number
}

export interface ApiResponse<T> {
  code: number
  message: string
  data?: T
  success: boolean
}

// API请求函数
async function apiRequestWithAuth<T>(url: string, options: RequestInit = {}): Promise<T> {
  const response = await authFetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  })

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
  }

  const result: ApiResponse<T> = await response.json()
  
  if (!result.success) {
    throw new Error(result.message || '请求失败')
  }

  return result.data as T
}

/**
 * 创建膳食记录
 * @param request 膳食记录请求
 * @returns 膳食记录响应
 */
export async function createMealRecord(request: MealRecordRequest): Promise<MealRecordResponse> {
  return apiRequestWithAuth<MealRecordResponse>(API_BASE_URL, {
    method: 'POST',
    body: JSON.stringify(request),
  })
}

/**
 * 更新膳食记录
 * @param recordId 膳食记录ID
 * @param request 膳食记录请求
 * @returns 膳食记录响应
 */
export async function updateMealRecord(recordId: number, request: MealRecordRequest): Promise<MealRecordResponse> {
  return apiRequestWithAuth<MealRecordResponse>(`${API_BASE_URL}/${recordId}`, {
    method: 'PUT',
    body: JSON.stringify(request),
  })
}

/**
 * 删除膳食记录
 * @param recordId 膳食记录ID
 */
export async function deleteMealRecord(recordId: number): Promise<void> {
  return apiRequestWithAuth<void>(`${API_BASE_URL}/${recordId}`, {
    method: 'DELETE',
  })
}

/**
 * 获取膳食记录详情
 * @param recordId 膳食记录ID
 * @returns 膳食记录响应
 */
export async function getMealRecordById(recordId: number): Promise<MealRecordResponse> {
  return apiRequestWithAuth<MealRecordResponse>(`${API_BASE_URL}/${recordId}`)
}

/**
 * 获取指定日期的膳食记录
 * @param date 日期 (YYYY-MM-DD格式)
 * @returns 膳食记录列表
 */
export async function getMealRecordsByDate(date: string): Promise<MealRecordResponse[]> {
  return apiRequestWithAuth<MealRecordResponse[]>(`${API_BASE_URL}/date/${date}`)
}

/**
 * 获取日期范围内的膳食记录
 * @param startDate 开始日期 (YYYY-MM-DD格式)
 * @param endDate 结束日期 (YYYY-MM-DD格式)
 * @returns 膳食记录列表
 */
export async function getMealRecordsByDateRange(startDate: string, endDate: string): Promise<MealRecordResponse[]> {
  const url = `${API_BASE_URL}/range?startDate=${startDate}&endDate=${endDate}`
  return apiRequestWithAuth<MealRecordResponse[]>(url)
}

/**
 * 获取最近的膳食记录
 * @param limit 限制数量，默认10
 * @returns 膳食记录列表
 */
export async function getRecentMealRecords(limit: number = 10): Promise<MealRecordResponse[]> {
  const url = `${API_BASE_URL}/recent?limit=${limit}`
  return apiRequestWithAuth<MealRecordResponse[]>(url)
}

/**
 * 向膳食记录添加食物
 * @param recordId 膳食记录ID
 * @param foodId 食物ID
 * @param weight 重量(克)
 * @returns 更新后的膳食记录
 */
export async function addFoodToMealRecord(recordId: number, foodId: number, weight: number): Promise<MealRecordResponse> {
  const url = `${API_BASE_URL}/${recordId}/foods?foodId=${foodId}&weight=${weight}`
  return apiRequestWithAuth<MealRecordResponse>(url, {
    method: 'POST',
  })
}

/**
 * 从膳食记录移除食物
 * @param recordId 膳食记录ID
 * @param itemId 膳食条目ID
 * @returns 更新后的膳食记录
 */
export async function removeFoodFromMealRecord(recordId: number, itemId: number): Promise<MealRecordResponse> {
  return apiRequestWithAuth<MealRecordResponse>(`${API_BASE_URL}/${recordId}/items/${itemId}`, {
    method: 'DELETE',
  })
}

/**
 * 更新膳食条目重量
 * @param recordId 膳食记录ID
 * @param itemId 膳食条目ID
 * @param weight 新重量(克)
 * @returns 更新后的膳食记录
 */
export async function updateMealItemWeight(recordId: number, itemId: number, weight: number): Promise<MealRecordResponse> {
  const url = `${API_BASE_URL}/${recordId}/items/${itemId}/weight?weight=${weight}`
  return apiRequestWithAuth<MealRecordResponse>(url, {
    method: 'PUT',
  })
}

/**
 * 获取膳食记录统计信息
 * @param startDate 开始日期 (YYYY-MM-DD格式)
 * @param endDate 结束日期 (YYYY-MM-DD格式)
 * @returns 统计信息
 */
export async function getMealRecordStatistics(startDate: string, endDate: string): Promise<MealRecordStatistics> {
  const url = `${API_BASE_URL}/statistics?startDate=${startDate}&endDate=${endDate}`
  return apiRequestWithAuth<MealRecordStatistics>(url)
}

/**
 * 获取膳食类型显示名称
 * @param mealType 膳食类型
 * @returns 显示名称
 */
export function getMealTypeDisplayName(mealType: string): string {
  const mealTypeMap: Record<string, string> = {
    breakfast: '早餐',
    lunch: '午餐',
    dinner: '晚餐',
    snack: '加餐'
  }
  return mealTypeMap[mealType] || mealType
}

/**
 * 格式化营养值显示
 * @param value 营养值
 * @param unit 单位
 * @returns 格式化后的字符串
 */
export function formatNutritionValue(value: number | null | undefined, unit: string = ''): string {
  if (value === null || value === undefined || isNaN(value)) {
    return '-'
  }
  
  // 保留1位小数，去除不必要的0
  const formatted = parseFloat(value.toFixed(1)).toString()
  return unit ? `${formatted}${unit}` : formatted
}

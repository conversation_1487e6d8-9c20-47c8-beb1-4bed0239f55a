package com.example.meals.dto;

import com.example.meals.entity.ChinaFood;

import java.math.BigDecimal;

/**
 * 食物搜索响应DTO
 */
public class FoodSearchResponse {
    
    private Long id;
    private String foodCode;
    private String foodName;
    private Integer energyKcal;
    private BigDecimal protein;
    private BigDecimal fat;
    private BigDecimal cho;
    private String category;
    
    public FoodSearchResponse() {}
    
    public FoodSearchResponse(ChinaFood food) {
        this.id = food.getId();
        this.foodCode = food.getFoodCode();
        this.foodName = food.getFoodName();
        this.energyKcal = food.getEnergyKcal();
        this.protein = food.getProtein();
        this.fat = food.getFat();
        this.cho = food.getCho();
        this.category = "未知分类"; // 默认值，将由Service设置
    }

    public FoodSearchResponse(ChinaFood food, String categoryName) {
        this(food);
        this.category = categoryName != null ? categoryName : "未知分类";
    }


    // Getter and Setter methods
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFoodCode() {
        return foodCode;
    }

    public void setFoodCode(String foodCode) {
        this.foodCode = foodCode;
    }

    public String getFoodName() {
        return foodName;
    }

    public void setFoodName(String foodName) {
        this.foodName = foodName;
    }

    public Integer getEnergyKcal() {
        return energyKcal;
    }

    public void setEnergyKcal(Integer energyKcal) {
        this.energyKcal = energyKcal;
    }

    public BigDecimal getProtein() {
        return protein;
    }

    public void setProtein(BigDecimal protein) {
        this.protein = protein;
    }

    public BigDecimal getFat() {
        return fat;
    }

    public void setFat(BigDecimal fat) {
        this.fat = fat;
    }

    public BigDecimal getCho() {
        return cho;
    }

    public void setCho(BigDecimal cho) {
        this.cho = cho;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }
}

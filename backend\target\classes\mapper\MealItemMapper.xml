<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.meals.mapper.MealItemMapper">

    <!-- {{ AURA-X: Add - 创建膳食条目MyBatis映射文件. Approval: 寸止. }} -->
    <!-- 结果映射 -->
    <resultMap id="MealItemResultMap" type="com.example.meals.entity.MealItem">
        <id property="id" column="id"/>
        <result property="mealRecordId" column="meal_record_id"/>
        <result property="foodId" column="food_id"/>
        <result property="foodName" column="food_name"/>
        <result property="foodCode" column="food_code"/>
        <result property="weight" column="weight"/>
        <result property="energy" column="energy"/>
        <result property="protein" column="protein"/>
        <result property="fat" column="fat"/>
        <result property="carbohydrate" column="carbohydrate"/>
        <result property="dietaryFiber" column="dietary_fiber"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <!-- 插入膳食条目 -->
    <insert id="insert" parameterType="com.example.meals.entity.MealItem" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO meal_item (
            meal_record_id, food_id, food_name, food_code, weight,
            energy, protein, fat, carbohydrate, dietary_fiber
        ) VALUES (
            #{mealRecordId}, #{foodId}, #{foodName}, #{foodCode}, #{weight},
            #{energy}, #{protein}, #{fat}, #{carbohydrate}, #{dietaryFiber}
        )
    </insert>

    <!-- 批量插入膳食条目 -->
    <insert id="batchInsert" parameterType="list">
        INSERT INTO meal_item (
            meal_record_id, food_id, food_name, food_code, weight,
            energy, protein, fat, carbohydrate, dietary_fiber
        ) VALUES
        <foreach collection="mealItems" item="item" separator=",">
            (#{item.mealRecordId}, #{item.foodId}, #{item.foodName}, #{item.foodCode}, #{item.weight},
             #{item.energy}, #{item.protein}, #{item.fat}, #{item.carbohydrate}, #{item.dietaryFiber})
        </foreach>
    </insert>

    <!-- 更新膳食条目 -->
    <update id="update" parameterType="com.example.meals.entity.MealItem">
        UPDATE meal_item SET
            food_id = #{foodId},
            food_name = #{foodName},
            food_code = #{foodCode},
            weight = #{weight},
            energy = #{energy},
            protein = #{protein},
            fat = #{fat},
            carbohydrate = #{carbohydrate},
            dietary_fiber = #{dietaryFiber}
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除膳食条目 -->
    <delete id="deleteById" parameterType="long">
        DELETE FROM meal_item WHERE id = #{id}
    </delete>

    <!-- 根据膳食记录ID删除所有膳食条目 -->
    <delete id="deleteByMealRecordId" parameterType="long">
        DELETE FROM meal_item WHERE meal_record_id = #{mealRecordId}
    </delete>

    <!-- 根据ID查询膳食条目 -->
    <select id="selectById" parameterType="long" resultMap="MealItemResultMap">
        SELECT * FROM meal_item WHERE id = #{id}
    </select>

    <!-- 根据膳食记录ID查询膳食条目列表 -->
    <select id="selectByMealRecordId" parameterType="long" resultMap="MealItemResultMap">
        SELECT * FROM meal_item 
        WHERE meal_record_id = #{mealRecordId}
        ORDER BY create_time ASC
    </select>

    <!-- 根据膳食记录ID列表查询膳食条目列表 -->
    <select id="selectByMealRecordIds" resultMap="MealItemResultMap">
        SELECT * FROM meal_item 
        WHERE meal_record_id IN
        <foreach collection="mealRecordIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY meal_record_id, create_time ASC
    </select>

    <!-- 根据食物ID查询膳食条目列表 -->
    <select id="selectByFoodId" resultMap="MealItemResultMap">
        SELECT * FROM meal_item 
        WHERE food_id = #{foodId}
        ORDER BY create_time DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 统计膳食记录的条目数量 -->
    <select id="countByMealRecordId" parameterType="long" resultType="int">
        SELECT COUNT(*) FROM meal_item WHERE meal_record_id = #{mealRecordId}
    </select>

    <!-- 计算膳食记录的营养总值 -->
    <select id="calculateNutritionTotals" parameterType="long" resultType="map">
        SELECT 
            COALESCE(SUM(energy), 0) as totalEnergy,
            COALESCE(SUM(protein), 0) as totalProtein,
            COALESCE(SUM(fat), 0) as totalFat,
            COALESCE(SUM(carbohydrate), 0) as totalCarbohydrate,
            COALESCE(SUM(dietary_fiber), 0) as totalDietaryFiber
        FROM meal_item 
        WHERE meal_record_id = #{mealRecordId}
    </select>

</mapper>

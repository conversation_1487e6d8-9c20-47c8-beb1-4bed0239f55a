package com.example.meals.mapper;

import com.example.meals.entity.UserGoalHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 用户健康目标历史记录数据访问接口
 */
@Mapper
public interface UserGoalHistoryMapper {

    /**
     * 根据目标ID查询历史记录
     */
    List<UserGoalHistory> findByGoalId(@Param("goalId") Long goalId, @Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 根据目标ID和日期范围查询历史记录
     */
    List<UserGoalHistory> findByGoalIdAndDateRange(@Param("goalId") Long goalId,
                                                   @Param("userId") Long userId,
                                                   @Param("startDate") LocalDate startDate,
                                                   @Param("endDate") LocalDate endDate);

    /**
     * 根据用户ID查询最近的历史记录
     */
    List<UserGoalHistory> findRecentByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 插入历史记录
     */
    int insert(UserGoalHistory history);

    /**
     * 更新或插入历史记录（如果当天已有记录则更新，否则插入）
     */
    int insertOrUpdate(UserGoalHistory history);

    /**
     * 根据目标ID和日期查询历史记录
     */
    UserGoalHistory findByGoalIdAndDate(@Param("goalId") Long goalId,
                                       @Param("userId") Long userId,
                                       @Param("recordDate") LocalDate recordDate);

    /**
     * 删除目标的所有历史记录
     */
    int deleteByGoalId(@Param("goalId") Long goalId, @Param("userId") Long userId);

    /**
     * 删除指定日期的历史记录
     */
    int deleteByGoalIdAndDate(@Param("goalId") Long goalId,
                             @Param("userId") Long userId,
                             @Param("recordDate") LocalDate recordDate);

    /**
     * 统计目标的历史记录数量
     */
    int countByGoalId(@Param("goalId") Long goalId, @Param("userId") Long userId);

    /**
     * 获取用户连续记录天数（简化版本，实际可能需要更复杂的逻辑）
     */
    int getStreakDays(@Param("userId") Long userId, @Param("days") Integer days);
}

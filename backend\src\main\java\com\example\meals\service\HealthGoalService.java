package com.example.meals.service;

import com.example.meals.common.Result;
import com.example.meals.dto.HealthGoalResponse;
import com.example.meals.dto.UserGoalResponse;
import com.example.meals.dto.UserGoalStatsResponse;
import com.example.meals.dto.GoalHistoryResponse;
import com.example.meals.dto.request.HealthGoalRequest;
import com.example.meals.dto.request.CreateUserGoalRequest;
import com.example.meals.dto.request.UpdateUserGoalRequest;

import java.util.List;

/**
 * 健康目标服务接口
 */
public interface HealthGoalService {
    
    /**
     * 获取所有健康目标
     */
    Result<List<HealthGoalResponse>> getAllHealthGoals();
    
    /**
     * 获取启用的健康目标
     */
    Result<List<HealthGoalResponse>> getEnabledHealthGoals();
    
    /**
     * 根据ID获取健康目标
     */
    Result<HealthGoalResponse> getHealthGoalById(Long id);
    
    /**
     * 创建健康目标
     */
    Result<HealthGoalResponse> createHealthGoal(HealthGoalRequest request);
    
    /**
     * 更新健康目标
     */
    Result<HealthGoalResponse> updateHealthGoal(Long id, HealthGoalRequest request);
    
    /**
     * 删除健康目标
     */
    Result<Void> deleteHealthGoal(Long id);

    // ==================== 用户健康目标管理方法 ====================

    /**
     * 获取用户的健康目标列表
     */
    Result<List<UserGoalResponse>> getUserGoals(Long userId);

    /**
     * 创建用户健康目标
     */
    Result<UserGoalResponse> createUserGoal(Long userId, CreateUserGoalRequest request);

    /**
     * 更新用户健康目标
     */
    Result<UserGoalResponse> updateUserGoal(Long userId, Long goalId, UpdateUserGoalRequest request);

    /**
     * 删除用户健康目标
     */
    Result<Void> deleteUserGoal(Long userId, Long goalId);

    /**
     * 更新用户健康目标进度
     */
    Result<UserGoalResponse> updateGoalProgress(Long userId, Long goalId, Double currentValue);

    /**
     * 获取用户健康目标统计数据
     */
    Result<UserGoalStatsResponse> getUserGoalStats(Long userId);

    /**
     * 获取用户健康目标历史记录
     */
    Result<List<GoalHistoryResponse>> getGoalHistory(Long userId, Long goalId, Integer days);
}

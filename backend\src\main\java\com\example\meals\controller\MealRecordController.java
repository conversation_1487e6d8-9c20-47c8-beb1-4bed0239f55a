package com.example.meals.controller;

import com.example.meals.dto.MealRecordRequest;
import com.example.meals.dto.MealRecordResponse;
import com.example.meals.service.MealRecordService;
import com.example.meals.common.Result;
import com.example.meals.utils.AuthUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 膳食记录控制器
 * {{ AURA-X: Add - 创建膳食记录控制器. Approval: 寸止. }}
 */
@RestController
@RequestMapping("/api/meal-records")
public class MealRecordController {
    
    @Autowired
    private MealRecordService mealRecordService;
    
    /**
     * 创建膳食记录
     */
    @PostMapping
    public Result<MealRecordResponse> createMealRecord(
            @RequestBody MealRecordRequest request,
            HttpServletRequest httpRequest) {
        // 获取当前用户ID
        Long userId = AuthUtil.getCurrentUserId(httpRequest);
        if (userId == null) {
            return Result.unauthorized("请先登录");
        }
        
        return mealRecordService.createMealRecord(userId, request);
    }
    
    /**
     * 更新膳食记录
     */
    @PutMapping("/{recordId}")
    public Result<MealRecordResponse> updateMealRecord(
            @PathVariable Long recordId,
            @RequestBody MealRecordRequest request,
            HttpServletRequest httpRequest) {
        // 获取当前用户ID
        Long userId = AuthUtil.getCurrentUserId(httpRequest);
        if (userId == null) {
            return Result.unauthorized("请先登录");
        }
        
        return mealRecordService.updateMealRecord(userId, recordId, request);
    }
    
    /**
     * 删除膳食记录
     */
    @DeleteMapping("/{recordId}")
    public Result<Void> deleteMealRecord(
            @PathVariable Long recordId,
            HttpServletRequest httpRequest) {
        // 获取当前用户ID
        Long userId = AuthUtil.getCurrentUserId(httpRequest);
        if (userId == null) {
            return Result.unauthorized("请先登录");
        }
        
        return mealRecordService.deleteMealRecord(userId, recordId);
    }
    
    /**
     * 获取膳食记录详情
     */
    @GetMapping("/{recordId}")
    public Result<MealRecordResponse> getMealRecordById(
            @PathVariable Long recordId,
            HttpServletRequest httpRequest) {
        // 获取当前用户ID
        Long userId = AuthUtil.getCurrentUserId(httpRequest);
        if (userId == null) {
            return Result.unauthorized("请先登录");
        }
        
        return mealRecordService.getMealRecordById(userId, recordId);
    }
    
    /**
     * 获取指定日期的膳食记录
     */
    @GetMapping("/date/{date}")
    public Result<List<MealRecordResponse>> getMealRecordsByDate(
            @PathVariable String date,
            HttpServletRequest httpRequest) {
        // 获取当前用户ID
        Long userId = AuthUtil.getCurrentUserId(httpRequest);
        if (userId == null) {
            return Result.unauthorized("请先登录");
        }
        
        try {
            LocalDate recordDate = LocalDate.parse(date);
            return mealRecordService.getMealRecordsByDate(userId, recordDate);
        } catch (Exception e) {
            return Result.badRequest("日期格式错误，请使用YYYY-MM-DD格式");
        }
    }
    
    /**
     * 获取日期范围内的膳食记录
     */
    @GetMapping("/range")
    public Result<List<MealRecordResponse>> getMealRecordsByDateRange(
            @RequestParam String startDate,
            @RequestParam String endDate,
            HttpServletRequest httpRequest) {
        // 获取当前用户ID
        Long userId = AuthUtil.getCurrentUserId(httpRequest);
        if (userId == null) {
            return Result.unauthorized("请先登录");
        }
        
        try {
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);
            return mealRecordService.getMealRecordsByDateRange(userId, start, end);
        } catch (Exception e) {
            return Result.badRequest("日期格式错误，请使用YYYY-MM-DD格式");
        }
    }
    
    /**
     * 获取最近的膳食记录
     */
    @GetMapping("/recent")
    public Result<List<MealRecordResponse>> getRecentMealRecords(
            @RequestParam(defaultValue = "10") Integer limit,
            HttpServletRequest httpRequest) {
        // 获取当前用户ID
        Long userId = AuthUtil.getCurrentUserId(httpRequest);
        if (userId == null) {
            return Result.unauthorized("请先登录");
        }
        
        return mealRecordService.getRecentMealRecords(userId, limit);
    }
    
    /**
     * 向膳食记录添加食物
     */
    @PostMapping("/{recordId}/foods")
    public Result<MealRecordResponse> addFoodToMealRecord(
            @PathVariable Long recordId,
            @RequestParam Long foodId,
            @RequestParam BigDecimal weight,
            HttpServletRequest httpRequest) {
        // 获取当前用户ID
        Long userId = AuthUtil.getCurrentUserId(httpRequest);
        if (userId == null) {
            return Result.unauthorized("请先登录");
        }
        
        return mealRecordService.addFoodToMealRecord(userId, recordId, foodId, weight);
    }
    
    /**
     * 从膳食记录移除食物
     */
    @DeleteMapping("/{recordId}/items/{itemId}")
    public Result<MealRecordResponse> removeFoodFromMealRecord(
            @PathVariable Long recordId,
            @PathVariable Long itemId,
            HttpServletRequest httpRequest) {
        // 获取当前用户ID
        Long userId = AuthUtil.getCurrentUserId(httpRequest);
        if (userId == null) {
            return Result.unauthorized("请先登录");
        }
        
        return mealRecordService.removeFoodFromMealRecord(userId, recordId, itemId);
    }
    
    /**
     * 更新膳食条目重量
     */
    @PutMapping("/{recordId}/items/{itemId}/weight")
    public Result<MealRecordResponse> updateMealItemWeight(
            @PathVariable Long recordId,
            @PathVariable Long itemId,
            @RequestParam BigDecimal weight,
            HttpServletRequest httpRequest) {
        // 获取当前用户ID
        Long userId = AuthUtil.getCurrentUserId(httpRequest);
        if (userId == null) {
            return Result.unauthorized("请先登录");
        }
        
        return mealRecordService.updateMealItemWeight(userId, recordId, itemId, weight);
    }
    
    /**
     * 获取膳食记录统计信息
     */
    @GetMapping("/statistics")
    public Result<MealRecordService.MealRecordStatistics> getMealRecordStatistics(
            @RequestParam String startDate,
            @RequestParam String endDate,
            HttpServletRequest httpRequest) {
        // 获取当前用户ID
        Long userId = AuthUtil.getCurrentUserId(httpRequest);
        if (userId == null) {
            return Result.unauthorized("请先登录");
        }
        
        try {
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);
            return mealRecordService.getMealRecordStatistics(userId, start, end);
        } catch (Exception e) {
            return Result.badRequest("日期格式错误，请使用YYYY-MM-DD格式");
        }
    }
}

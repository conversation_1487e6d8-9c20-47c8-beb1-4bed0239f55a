package com.example.meals.service;

import com.example.meals.common.Result;
import com.example.meals.dto.FoodSearchResponse;
import com.example.meals.dto.NutritionResponse;

import java.util.List;

/**
 * 食物服务接口
 */
public interface FoodService {
    
    /**
     * 搜索食物
     * @param keyword 搜索关键词
     * @param limit 限制返回数量，默认10
     * @return 食物搜索结果
     */
    Result<List<FoodSearchResponse>> searchFoods(String keyword, Integer limit);
    
    /**
     * 获取所有食物分类
     * @return 分类列表
     */
    Result<List<String>> getAllCategories();
    
    /**
     * 根据分类获取食物列表
     * @param categoryCode 分类代码
     * @param limit 限制返回数量，默认20
     * @return 食物列表
     */
    Result<List<FoodSearchResponse>> getFoodsByCategory(String categoryCode, Integer limit);
    
    /**
     * 获取热门食物
     * @param limit 限制返回数量，默认6
     * @return 热门食物列表
     */
    Result<List<FoodSearchResponse>> getPopularFoods(Integer limit);
    
    /**
     * 分析食物营养成分
     * @param foodId 食物ID
     * @param weight 重量(克)，默认100克
     * @return 营养分析结果
     */
    Result<NutritionResponse> analyzeNutrition(Long foodId, Integer weight);
    
    /**
     * 根据ID获取食物详情
     * @param foodId 食物ID
     * @return 食物详情
     */
    Result<FoodSearchResponse> getFoodById(Long foodId);
}

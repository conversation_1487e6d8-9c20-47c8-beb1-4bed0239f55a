package com.example.meals.entity;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户健康目标历史记录实体类
 */
public class UserGoalHistory {
    
    private Long id;
    private Long goalId;
    private Long userId;
    private LocalDate recordDate;
    private Double value;
    private Double progress;
    private String notes;
    private LocalDateTime createdAt;
    
    // 构造函数
    public UserGoalHistory() {}
    
    public UserGoalHistory(Long goalId, Long userId, LocalDate recordDate, Double value, Double progress) {
        this.goalId = goalId;
        this.userId = userId;
        this.recordDate = recordDate;
        this.value = value;
        this.progress = progress;
    }
    
    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getGoalId() {
        return goalId;
    }
    
    public void setGoalId(Long goalId) {
        this.goalId = goalId;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public LocalDate getRecordDate() {
        return recordDate;
    }
    
    public void setRecordDate(LocalDate recordDate) {
        this.recordDate = recordDate;
    }
    
    public Double getValue() {
        return value;
    }
    
    public void setValue(Double value) {
        this.value = value;
    }
    
    public Double getProgress() {
        return progress;
    }
    
    public void setProgress(Double progress) {
        this.progress = progress;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    @Override
    public String toString() {
        return "UserGoalHistory{" +
                "id=" + id +
                ", goalId=" + goalId +
                ", userId=" + userId +
                ", recordDate=" + recordDate +
                ", value=" + value +
                ", progress=" + progress +
                ", notes='" + notes + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}

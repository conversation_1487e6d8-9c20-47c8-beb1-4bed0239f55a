// 食物相关API接口

// 基础配置
const API_BASE_URL = 'http://localhost:8080/api/foods'

// 类型定义
export interface FoodItem {
  id: number
  foodCode: string
  foodName: string
  energyKcal: number
  protein: number
  fat: number
  cho: number
  category: string
}

export interface NutritionAnalysis {
  foodInfo: {
    id: number
    name: string
    code: string
    category: string
    weight: number
  }
  calculatedNutrition: {
    calories: number
    protein: number
    fat: number
    carbohydrates: number
    fiber: number
    sugar: number | null
    sodium: number
    cholesterol: number
  }
  detailedNutrients: {
    vitaminA: number
    vitaminC: number
    vitaminE: number
    thiamin: number
    riboflavin: number
    niacin: number
    calcium: number
    iron: number
    zinc: number
    selenium: number
    phosphorus: number
    potassium: number
    magnesium: number
    copper: number
    manganese: number
    water: number
    ash: number
  }
}

export interface ApiResponse<T> {
  code: number
  message: string
  data?: T
  success: boolean
}

// API请求函数
async function apiRequest<T>(url: string, options: RequestInit = {}): Promise<T> {
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  })

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
  }

  const result: ApiResponse<T> = await response.json()
  
  if (!result.success) {
    throw new Error(result.message || '请求失败')
  }

  return result.data as T
}

/**
 * 搜索食物
 * @param keyword 搜索关键词
 * @param limit 限制返回数量，默认10
 * @returns 食物搜索结果
 */
export async function searchFoods(keyword: string, limit: number = 10): Promise<FoodItem[]> {
  const url = `${API_BASE_URL}/search?keyword=${encodeURIComponent(keyword)}&limit=${limit}`
  return apiRequest<FoodItem[]>(url)
}

/**
 * 获取所有食物分类
 * @returns 分类列表
 */
export async function getAllCategories(): Promise<string[]> {
  const url = `${API_BASE_URL}/categories`
  return apiRequest<string[]>(url)
}

/**
 * 根据分类获取食物列表
 * @param categoryCode 分类代码
 * @param limit 限制返回数量，默认20
 * @returns 食物列表
 */
export async function getFoodsByCategory(categoryCode: string, limit: number = 20): Promise<FoodItem[]> {
  const url = `${API_BASE_URL}/category/${categoryCode}?limit=${limit}`
  return apiRequest<FoodItem[]>(url)
}

/**
 * 获取热门食物
 * @param limit 限制返回数量，默认6
 * @returns 热门食物列表
 */
export async function getPopularFoods(limit: number = 6): Promise<FoodItem[]> {
  const url = `${API_BASE_URL}/popular?limit=${limit}`
  return apiRequest<FoodItem[]>(url)
}

/**
 * 分析食物营养成分
 * @param foodId 食物ID
 * @param weight 重量(克)，默认100克
 * @returns 营养分析结果
 */
export async function analyzeNutrition(foodId: number, weight: number = 100): Promise<NutritionAnalysis> {
  const url = `${API_BASE_URL}/${foodId}/nutrition?weight=${weight}`
  return apiRequest<NutritionAnalysis>(url)
}

/**
 * 根据ID获取食物详情
 * @param foodId 食物ID
 * @returns 食物详情
 */
export async function getFoodById(foodId: number): Promise<FoodItem> {
  const url = `${API_BASE_URL}/${foodId}`
  return apiRequest<FoodItem>(url)
}

/**
 * 格式化营养成分值
 * @param value 营养成分值
 * @param unit 单位
 * @returns 格式化后的字符串
 */
export function formatNutritionValue(value: number | null | undefined, unit: string): string {
  if (value === null || value === undefined || isNaN(value)) {
    return `0 ${unit}`
  }
  
  // 根据数值大小决定小数位数
  if (value >= 100) {
    return `${value.toFixed(0)} ${unit}`
  } else if (value >= 10) {
    return `${value.toFixed(1)} ${unit}`
  } else {
    return `${value.toFixed(2)} ${unit}`
  }
}

/**
 * 获取营养成分等级颜色
 * @param value 营养成分值
 * @param type 营养成分类型
 * @returns CSS颜色类名
 */
export function getNutritionLevelColor(value: number | null | undefined, type: string): string {
  if (value === null || value === undefined || isNaN(value)) {
    return 'low'
  }
  
  // 根据不同营养成分类型设置不同的阈值
  switch (type) {
    case 'calories':
      if (value >= 400) return 'high'
      if (value >= 200) return 'medium'
      return 'low'
    
    case 'protein':
      if (value >= 20) return 'high'
      if (value >= 10) return 'medium'
      return 'low'
    
    case 'fat':
      if (value >= 20) return 'high'
      if (value >= 10) return 'medium'
      return 'low'
    
    case 'carbohydrates':
      if (value >= 50) return 'high'
      if (value >= 25) return 'medium'
      return 'low'
    
    case 'fiber':
      if (value >= 6) return 'high'
      if (value >= 3) return 'medium'
      return 'low'
    
    case 'sodium':
      if (value >= 600) return 'high'
      if (value >= 300) return 'medium'
      return 'low'
    
    case 'calcium':
      if (value >= 300) return 'high'
      if (value >= 150) return 'medium'
      return 'low'
    
    case 'iron':
      if (value >= 10) return 'high'
      if (value >= 5) return 'medium'
      return 'low'
    
    case 'vitaminC':
      if (value >= 60) return 'high'
      if (value >= 30) return 'medium'
      return 'low'
    
    default:
      return 'medium'
  }
}

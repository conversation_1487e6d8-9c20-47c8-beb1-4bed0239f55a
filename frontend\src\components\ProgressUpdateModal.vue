<template>
  <div v-if="show" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <!-- 模态框头部 -->
      <div class="modal-header">
        <h2 class="modal-title">
          <span class="title-icon">📊</span>
          更新进度
        </h2>
        <button @click="$emit('close')" class="close-btn">
          <span>✕</span>
        </button>
      </div>

      <!-- 模态框内容 -->
      <div class="modal-body" v-if="goal">
        <!-- 目标信息 -->
        <div class="goal-info">
          <div class="goal-header">
            <div class="goal-icon">{{ getGoalIcon(goal.goalType) }}</div>
            <div class="goal-details">
              <h3 class="goal-name">{{ goal.goalName }}</h3>
              <p class="goal-target">目标: {{ goal.targetValue }} {{ goal.unit }}</p>
            </div>
          </div>
        </div>

        <!-- 当前进度显示 -->
        <div class="current-progress">
          <div class="progress-circle">
            <svg class="progress-ring" width="120" height="120">
              <circle
                class="progress-ring-background"
                cx="60"
                cy="60"
                r="50"
                fill="transparent"
                stroke="currentColor"
                stroke-width="8"
              />
              <circle
                class="progress-ring-progress"
                cx="60"
                cy="60"
                r="50"
                fill="transparent"
                stroke="currentColor"
                stroke-width="8"
                :stroke-dasharray="circumference"
                :stroke-dashoffset="getStrokeDashoffset(currentProgress)"
                transform="rotate(-90 60 60)"
              />
            </svg>
            <div class="progress-text">
              <span class="progress-percentage">{{ Math.round(currentProgress) }}%</span>
              <span class="progress-label">完成度</span>
            </div>
          </div>
          
          <div class="progress-stats">
            <div class="stat-item">
              <span class="stat-label">当前值</span>
              <span class="stat-value">{{ newValue }} {{ goal.unit }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">目标值</span>
              <span class="stat-value">{{ goal.targetValue }} {{ goal.unit }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">剩余</span>
              <span class="stat-value" :class="{ 'completed': isCompleted }">
                {{ isCompleted ? '已完成' : `${remaining} ${goal.unit}` }}
              </span>
            </div>
          </div>
        </div>

        <!-- 进度输入 -->
        <div class="progress-input">
          <label class="input-label">更新当前值</label>
          <div class="input-group">
            <input
              v-model.number="newValue"
              type="number"
              class="value-input"
              :min="0"
              :step="getInputStep(goal.goalType)"
              :placeholder="`输入新的${goal.unit}值`"
            />
            <span class="input-unit">{{ goal.unit }}</span>
          </div>
          
          <!-- 快速调整按钮 -->
          <div class="quick-adjust">
            <button
              v-for="adjustment in getQuickAdjustments(goal.goalType)"
              :key="adjustment.value"
              @click="adjustValue(adjustment.value)"
              class="adjust-btn"
              :class="adjustment.type"
            >
              {{ adjustment.label }}
            </button>
          </div>
        </div>

        <!-- 进度历史（简化版） -->
        <div class="progress-history" v-if="recentHistory.length > 0">
          <h4 class="history-title">最近记录</h4>
          <div class="history-list">
            <div
              v-for="record in recentHistory"
              :key="record.date"
              class="history-item"
            >
              <span class="history-date">{{ formatHistoryDate(record.date) }}</span>
              <span class="history-value">{{ record.value }} {{ goal.unit }}</span>
              <span class="history-progress">{{ Math.round(record.progress) }}%</span>
            </div>
          </div>
        </div>

        <!-- 激励信息 -->
        <div class="motivation" v-if="motivationMessage">
          <div class="motivation-icon">{{ motivationIcon }}</div>
          <p class="motivation-text">{{ motivationMessage }}</p>
        </div>
      </div>

      <!-- 模态框底部 -->
      <div class="modal-footer">
        <button @click="$emit('close')" type="button" class="btn btn-secondary">
          取消
        </button>
        <button 
          @click="handleSubmit" 
          type="button" 
          class="btn btn-primary"
          :disabled="!isValidValue || isSubmitting"
        >
          <span v-if="isSubmitting" class="loading-spinner"></span>
          更新进度
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { calculateProgress } from '../utils/healthGoalApi'
import type { UserGoal } from '../utils/healthGoalApi'

// Props
interface Props {
  show: boolean
  goal?: UserGoal | null
}

const props = withDefaults(defineProps<Props>(), {
  goal: null
})

// Emits
const emit = defineEmits<{
  close: []
  submit: [value: number]
}>()

// 响应式数据
const isSubmitting = ref(false)
const newValue = ref(0)
const recentHistory = ref<Array<{ date: string; value: number; progress: number }>>([])

// 计算属性
const circumference = 2 * Math.PI * 50

const currentProgress = computed(() => {
  if (!props.goal) return 0
  return calculateProgress(newValue.value, props.goal.targetValue, props.goal.goalType)
})

const isCompleted = computed(() => {
  if (!props.goal) return false
  return newValue.value >= props.goal.targetValue
})

const remaining = computed(() => {
  if (!props.goal) return 0
  return Math.max(0, props.goal.targetValue - newValue.value)
})

const isValidValue = computed(() => {
  return newValue.value >= 0 && !isNaN(newValue.value)
})

const motivationMessage = computed(() => {
  if (!props.goal) return ''
  
  const progress = currentProgress.value
  if (progress >= 100) return '恭喜！你已经达成目标了！🎉'
  if (progress >= 80) return '太棒了！你离目标越来越近了！'
  if (progress >= 60) return '很好！继续保持这个势头！'
  if (progress >= 40) return '不错的进展，坚持下去！'
  if (progress >= 20) return '好的开始！每一步都很重要！'
  return '开始你的健康之旅吧！'
})

const motivationIcon = computed(() => {
  const progress = currentProgress.value
  if (progress >= 100) return '🏆'
  if (progress >= 80) return '🔥'
  if (progress >= 60) return '💪'
  if (progress >= 40) return '👍'
  if (progress >= 20) return '🌟'
  return '🚀'
})

// 方法
const getGoalIcon = (type: string): string => {
  const icons = {
    calories: '🔥',
    weight: '⚖️',
    exercise: '💪',
    nutrition: '🥗',
    water: '💧',
    sleep: '😴'
  }
  return icons[type as keyof typeof icons] || '🎯'
}

const getStrokeDashoffset = (progress: number): number => {
  return circumference - (progress / 100) * circumference
}

const getInputStep = (goalType: string): number => {
  const steps = {
    weight: 0.1,
    calories: 10,
    exercise: 5,
    water: 50,
    sleep: 0.5,
    nutrition: 1
  }
  return steps[goalType as keyof typeof steps] || 1
}

const getQuickAdjustments = (goalType: string) => {
  const adjustments = {
    calories: [
      { label: '+100', value: 100, type: 'positive' },
      { label: '+250', value: 250, type: 'positive' },
      { label: '+500', value: 500, type: 'positive' },
      { label: '-100', value: -100, type: 'negative' }
    ],
    weight: [
      { label: '+0.1kg', value: 0.1, type: 'positive' },
      { label: '+0.5kg', value: 0.5, type: 'positive' },
      { label: '-0.1kg', value: -0.1, type: 'negative' },
      { label: '-0.5kg', value: -0.5, type: 'negative' }
    ],
    exercise: [
      { label: '+15分钟', value: 15, type: 'positive' },
      { label: '+30分钟', value: 30, type: 'positive' },
      { label: '+60分钟', value: 60, type: 'positive' }
    ],
    water: [
      { label: '+250ml', value: 250, type: 'positive' },
      { label: '+500ml', value: 500, type: 'positive' },
      { label: '+1000ml', value: 1000, type: 'positive' }
    ],
    sleep: [
      { label: '+0.5小时', value: 0.5, type: 'positive' },
      { label: '+1小时', value: 1, type: 'positive' },
      { label: '-0.5小时', value: -0.5, type: 'negative' }
    ],
    nutrition: [
      { label: '+10g', value: 10, type: 'positive' },
      { label: '+25g', value: 25, type: 'positive' },
      { label: '+50g', value: 50, type: 'positive' }
    ]
  }
  return adjustments[goalType as keyof typeof adjustments] || []
}

const adjustValue = (adjustment: number) => {
  newValue.value = Math.max(0, newValue.value + adjustment)
}

const formatHistoryDate = (dateString: string): string => {
  const date = new Date(dateString)
  const today = new Date()
  const diffTime = today.getTime() - date.getTime()
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 0) return '今天'
  if (diffDays === 1) return '昨天'
  if (diffDays <= 7) return `${diffDays}天前`
  return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
}

const handleOverlayClick = () => {
  emit('close')
}

const handleSubmit = () => {
  if (!isValidValue.value || isSubmitting.value) return
  
  isSubmitting.value = true
  emit('submit', newValue.value)
  // 注意：isSubmitting 的重置应该在父组件处理完成后进行
}

// 监听目标变化
watch(() => props.goal, (goal) => {
  if (goal) {
    newValue.value = goal.currentValue
    // TODO: 加载历史记录
    recentHistory.value = []
  }
}, { immediate: true })

// 监听提交状态重置
watch(() => props.show, (show) => {
  if (!show) {
    isSubmitting.value = false
  }
})
</script>

<style scoped>
/* 模态框基础样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
  animation: fadeIn 0.3s ease-out;
}

.modal-container {
  background: white;
  border-radius: 24px;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  animation: slideInScale 0.4s ease-out;
  position: relative;
}

.modal-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

/* 头部样式 */
.modal-header {
  padding: 2rem 2rem 1rem 2rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.title-icon {
  font-size: 2rem;
}

.close-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.05);
  color: #718096;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(0, 0, 0, 0.1);
  transform: scale(1.1);
}

/* 内容区域 */
.modal-body {
  padding: 1rem 2rem 2rem 2rem;
  max-height: 60vh;
  overflow-y: auto;
}

/* 目标信息 */
.goal-info {
  margin-bottom: 2rem;
}

.goal-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border-radius: 16px;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.goal-icon {
  font-size: 2.5rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.goal-details {
  flex: 1;
}

.goal-name {
  font-size: 1.3rem;
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 0.3rem 0;
}

.goal-target {
  font-size: 1rem;
  color: #718096;
  margin: 0;
  font-weight: 500;
}

/* 当前进度显示 */
.current-progress {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 16px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.progress-circle {
  position: relative;
  flex-shrink: 0;
}

.progress-ring {
  width: 120px;
  height: 120px;
  transform: rotate(-90deg);
}

.progress-ring-background {
  stroke: rgba(0, 0, 0, 0.1);
  stroke-width: 8;
}

.progress-ring-progress {
  stroke: #667eea;
  stroke-width: 8;
  stroke-linecap: round;
  transition: stroke-dashoffset 0.8s ease-in-out;
  filter: drop-shadow(0 0 6px #667eea);
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.progress-percentage {
  display: block;
  font-size: 1.5rem;
  font-weight: 800;
  color: #2d3748;
  line-height: 1;
}

.progress-label {
  display: block;
  font-size: 0.8rem;
  color: #718096;
  margin-top: 0.2rem;
}

.progress-stats {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 0.9rem;
  color: #718096;
  font-weight: 500;
}

.stat-value {
  font-size: 1rem;
  color: #2d3748;
  font-weight: 600;
}

.stat-value.completed {
  color: #48bb78;
}

/* 进度输入 */
.progress-input {
  margin-bottom: 2rem;
}

.input-label {
  font-size: 1rem;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 0.8rem;
  display: block;
}

.input-group {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.value-input {
  flex: 1;
  padding: 1rem 4rem 1rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1.2rem;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
  background: white;
}

.value-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-unit {
  position: absolute;
  right: 1rem;
  color: #718096;
  font-weight: 600;
  font-size: 1rem;
  pointer-events: none;
}

/* 快速调整按钮 */
.quick-adjust {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
}

.adjust-btn {
  padding: 0.6rem 1.2rem;
  border: 2px solid #e2e8f0;
  border-radius: 20px;
  background: white;
  color: #4a5568;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.adjust-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.adjust-btn.positive {
  border-color: rgba(72, 187, 120, 0.3);
  color: #48bb78;
}

.adjust-btn.positive:hover {
  background: rgba(72, 187, 120, 0.1);
  border-color: #48bb78;
}

.adjust-btn.negative {
  border-color: rgba(245, 101, 101, 0.3);
  color: #f56565;
}

.adjust-btn.negative:hover {
  background: rgba(245, 101, 101, 0.1);
  border-color: #f56565;
}

/* 进度历史 */
.progress-history {
  margin-bottom: 2rem;
}

.history-title {
  font-size: 1rem;
  font-weight: 600;
  color: #4a5568;
  margin: 0 0 1rem 0;
}

.history-list {
  background: rgba(0, 0, 0, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.8rem 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.history-item:last-child {
  border-bottom: none;
}

.history-date {
  font-size: 0.9rem;
  color: #718096;
  font-weight: 500;
  min-width: 60px;
}

.history-value {
  font-size: 0.9rem;
  color: #2d3748;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.history-progress {
  font-size: 0.9rem;
  color: #667eea;
  font-weight: 600;
  min-width: 50px;
  text-align: right;
}

/* 激励信息 */
.motivation {
  background: linear-gradient(135deg, rgba(72, 187, 120, 0.1), rgba(56, 161, 105, 0.1));
  border: 1px solid rgba(72, 187, 120, 0.2);
  border-radius: 16px;
  padding: 1.5rem;
  text-align: center;
  margin-bottom: 1rem;
}

.motivation-icon {
  font-size: 2.5rem;
  margin-bottom: 0.8rem;
}

.motivation-text {
  font-size: 1rem;
  color: #2d3748;
  font-weight: 500;
  margin: 0;
  line-height: 1.5;
}

/* 底部按钮 */
.modal-footer {
  padding: 1.5rem 2rem 2rem 2rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.btn {
  padding: 0.875rem 2rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 120px;
  justify-content: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-secondary {
  background: #f7fafc;
  color: #4a5568;
  border: 2px solid #e2e8f0;
}

.btn-secondary:hover:not(:disabled) {
  background: #edf2f7;
  border-color: #cbd5e0;
  transform: translateY(-1px);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* 加载动画 */
.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 1rem;
  }

  .modal-container {
    max-height: 95vh;
  }

  .modal-header {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
  }

  .modal-body {
    padding: 1rem 1.5rem 1.5rem 1.5rem;
  }

  .modal-footer {
    padding: 1rem 1.5rem 1.5rem 1.5rem;
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }

  .current-progress {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }

  .quick-adjust {
    justify-content: center;
  }

  .adjust-btn {
    flex: 1;
    min-width: 80px;
  }
}

@media (max-width: 480px) {
  .modal-title {
    font-size: 1.5rem;
  }

  .goal-header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .progress-ring {
    width: 100px;
    height: 100px;
  }

  .progress-percentage {
    font-size: 1.2rem;
  }

  .quick-adjust {
    flex-direction: column;
  }

  .adjust-btn {
    width: 100%;
  }
}
</style>

<template>
  <div class="meals-container">
    <!-- {{ AURA-X: Modify - 重构膳食记录页面，实现完整功能. Approval: 寸止. }} -->
    <!-- 顶部导航栏 -->
    <TopNavbar />

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 页面标题 -->
      <section class="page-header">
        <h1 class="page-title">
          <span class="title-icon">🍽️</span>
          膳食记录
        </h1>
        <p class="page-subtitle">记录每日饮食，追踪营养摄入情况</p>
      </section>

      <!-- 日期选择器 -->
      <section class="date-selector-section">
        <div class="date-selector-card">
          <div class="date-controls">
            <button @click="previousDay" class="date-nav-btn">
              <span class="nav-icon">‹</span>
            </button>
            <div class="current-date">
              <input
                type="date"
                v-model="selectedDate"
                @change="onDateChange"
                class="date-input"
              />
              <span class="date-display">{{ formatDateDisplay(selectedDate) }}</span>
            </div>
            <button @click="nextDay" class="date-nav-btn">
              <span class="nav-icon">›</span>
            </button>
          </div>
          <div class="quick-date-buttons">
            <button @click="selectToday" class="quick-date-btn" :class="{ active: isToday }">今天</button>
            <button @click="selectYesterday" class="quick-date-btn">昨天</button>
          </div>
        </div>
      </section>

      <!-- 每日营养概览卡片 -->
      <section class="daily-overview-section">
        <div class="overview-card">
          <div class="overview-header">
            <h2 class="overview-title">
              <span class="overview-icon">📊</span>
              今日营养摄入
            </h2>
            <div class="overview-date">{{ formatDateDisplay(selectedDate) }}</div>
          </div>
          <div class="nutrition-overview">
            <div class="nutrition-circle-chart">
              <div class="chart-container">
                <div class="energy-circle">
                  <div class="energy-value">{{ getTotalNutrition('energy') }}</div>
                  <div class="energy-unit">kcal</div>
                </div>
              </div>
            </div>
            <div class="nutrition-details">
              <div class="nutrition-item">
                <div class="nutrition-bar">
                  <div class="bar-fill protein-bar" :style="{ width: getNutritionPercentage('protein') + '%' }"></div>
                </div>
                <div class="nutrition-info">
                  <span class="nutrition-name">蛋白质</span>
                  <span class="nutrition-amount">{{ getTotalNutrition('protein') }}g</span>
                </div>
              </div>
              <div class="nutrition-item">
                <div class="nutrition-bar">
                  <div class="bar-fill fat-bar" :style="{ width: getNutritionPercentage('fat') + '%' }"></div>
                </div>
                <div class="nutrition-info">
                  <span class="nutrition-name">脂肪</span>
                  <span class="nutrition-amount">{{ getTotalNutrition('fat') }}g</span>
                </div>
              </div>
              <div class="nutrition-item">
                <div class="nutrition-bar">
                  <div class="bar-fill carb-bar" :style="{ width: getNutritionPercentage('carbohydrate') + '%' }"></div>
                </div>
                <div class="nutrition-info">
                  <span class="nutrition-name">碳水化合物</span>
                  <span class="nutrition-amount">{{ getTotalNutrition('carbohydrate') }}g</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 膳食记录网格 -->
      <section class="meal-records-section">
        <!-- 加载状态 -->
        <div v-if="isLoading" class="loading-state">
          <div class="loading-spinner"></div>
          <p>正在加载膳食记录...</p>
        </div>

        <!-- 错误状态 -->
        <div v-if="error" class="error-state">
          <div class="error-icon">⚠️</div>
          <div class="error-content">
            <h4>加载失败</h4>
            <p>{{ error }}</p>
            <button @click="loadMealRecords" class="retry-btn">重试</button>
          </div>
        </div>

        <!-- 膳食记录网格布局 -->
        <div v-if="!isLoading && !error" class="meal-records-grid">
          <div
            v-for="mealType in mealTypes"
            :key="mealType.type"
            class="meal-card"
            :class="{ 'has-records': getMealRecordByType(mealType.type), [`meal-${mealType.type}`]: true }"
          >
            <div class="meal-card-header">
              <div class="meal-header-background"></div>
              <div class="meal-type-badge">
                <span class="meal-type-icon">{{ mealType.icon }}</span>
              </div>
              <div class="meal-header-content">
                <h3 class="meal-type-name">{{ mealType.name }}</h3>
                <span class="meal-type-time">{{ mealType.time }}</span>
                <div class="meal-energy-display">
                  {{ getMealEnergy(mealType.type) }} kcal
                </div>
              </div>
              <button
                @click="openAddFoodDialog(mealType.type)"
                class="floating-add-btn"
                :title="`添加${mealType.name}食物`"
              >
                <span class="add-icon">+</span>
              </button>
            </div>

            <!-- 膳食条目列表 -->
            <div class="meal-card-body">
              <template v-if="getMealRecordByType(mealType.type)">
                <div class="meal-items-grid">
                  <div
                    v-for="item in getMealRecordByType(mealType.type)?.mealItems"
                    :key="item.id"
                    class="meal-item-card"
                  >
                    <div class="item-header">
                      <span class="food-name">{{ item.foodName }}</span>
                      <div class="item-actions">
                        <button
                          @click="editMealItem(getMealRecordByType(mealType.type)!.id, item)"
                          class="action-btn edit-btn"
                          title="编辑"
                        >
                          ✏️
                        </button>
                        <button
                          @click="removeMealItem(getMealRecordByType(mealType.type)!.id, item.id)"
                          class="action-btn remove-btn"
                          title="删除"
                        >
                          🗑️
                        </button>
                      </div>
                    </div>
                    <div class="item-details">
                      <div class="weight-info">
                        <span class="weight-value">{{ formatNutritionValue(item.weight, 'g') }}</span>
                      </div>
                      <div class="energy-info">
                        <span class="energy-value">{{ formatNutritionValue(item.energy, 'kcal') }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
              <div v-else class="empty-meal-state">
                <div class="empty-icon">🍽️</div>
                <p class="empty-text">还没有添加食物</p>
                <button @click="openAddFoodDialog(mealType.type)" class="add-first-btn">
                  <span class="btn-icon">+</span>
                  添加第一个食物
                </button>
              </div>
            </div>

            <!-- 膳食营养汇总 -->
            <div v-if="getMealRecordByType(mealType.type)" class="meal-card-footer">
              <div class="nutrition-mini-stats">
                <div class="mini-stat">
                  <span class="stat-value">{{ formatNutritionValue(getMealRecordByType(mealType.type)?.totalProtein, 'g') }}</span>
                  <span class="stat-label">蛋白质</span>
                </div>
                <div class="mini-stat">
                  <span class="stat-value">{{ formatNutritionValue(getMealRecordByType(mealType.type)?.totalFat, 'g') }}</span>
                  <span class="stat-label">脂肪</span>
                </div>
                <div class="mini-stat">
                  <span class="stat-value">{{ formatNutritionValue(getMealRecordByType(mealType.type)?.totalCarbohydrate, 'g') }}</span>
                  <span class="stat-label">碳水</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- {{ AURA-X: Add - 集成食物选择对话框组件. Approval: 寸止. }} -->
    <!-- 食物选择对话框 -->
    <FoodSelectionDialog
      :is-visible="showFoodDialog"
      :meal-type="currentMealType"
      @close="closeFoodDialog"
      @confirm="onFoodSelected"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import TopNavbar from '../components/TopNavbar.vue'
import FoodSelectionDialog from '../components/FoodSelectionDialog.vue'
import {
  getMealRecordsByDate,
  createMealRecord,
  addFoodToMealRecord,
  removeFoodFromMealRecord,
  updateMealItemWeight,
  formatNutritionValue,
  type MealRecordResponse,
  type MealItemResponse,
  type MealRecordRequest
} from '../utils/mealRecordApi'
import { type FoodItem } from '../utils/foodApi'

// 响应式数据
const selectedDate = ref(new Date().toISOString().split('T')[0]) // 当前日期
const mealRecords = ref<MealRecordResponse[]>([])
const isLoading = ref(false)
const error = ref('')

// 食物选择对话框相关
const showFoodDialog = ref(false)
const currentMealType = ref('')

// 膳食类型配置
const mealTypes = [
  { type: 'breakfast', name: '早餐', icon: '🌅', time: '06:00-10:00' },
  { type: 'lunch', name: '午餐', icon: '☀️', time: '11:00-14:00' },
  { type: 'dinner', name: '晚餐', icon: '🌙', time: '17:00-21:00' },
  { type: 'snack', name: '加餐', icon: '🍎', time: '随时' }
]

// 计算属性
const isToday = computed(() => {
  const today = new Date().toISOString().split('T')[0]
  return selectedDate.value === today
})

// 方法
const formatDateDisplay = (dateStr: string): string => {
  const date = new Date(dateStr)
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)

  if (dateStr === today.toISOString().split('T')[0]) {
    return '今天'
  } else if (dateStr === yesterday.toISOString().split('T')[0]) {
    return '昨天'
  } else {
    return date.toLocaleDateString('zh-CN', {
      month: 'long',
      day: 'numeric',
      weekday: 'short'
    })
  }
}

const previousDay = () => {
  const date = new Date(selectedDate.value)
  date.setDate(date.getDate() - 1)
  selectedDate.value = date.toISOString().split('T')[0]
  loadMealRecords()
}

const nextDay = () => {
  const date = new Date(selectedDate.value)
  date.setDate(date.getDate() + 1)
  selectedDate.value = date.toISOString().split('T')[0]
  loadMealRecords()
}

const selectToday = () => {
  selectedDate.value = new Date().toISOString().split('T')[0]
  loadMealRecords()
}

const selectYesterday = () => {
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  selectedDate.value = yesterday.toISOString().split('T')[0]
  loadMealRecords()
}

const onDateChange = () => {
  loadMealRecords()
}

const getMealRecordByType = (mealType: string): MealRecordResponse | undefined => {
  return mealRecords.value.find(record => record.mealType === mealType)
}

const loadMealRecords = async () => {
  try {
    isLoading.value = true
    error.value = ''
    const records = await getMealRecordsByDate(selectedDate.value)
    mealRecords.value = records
  } catch (err) {
    console.error('加载膳食记录失败:', err)
    error.value = err instanceof Error ? err.message : '加载膳食记录失败'
  } finally {
    isLoading.value = false
  }
}

const openAddFoodDialog = (mealType: string) => {
  currentMealType.value = mealType
  showFoodDialog.value = true
}

const closeFoodDialog = () => {
  showFoodDialog.value = false
  currentMealType.value = ''
}

const onFoodSelected = async (food: FoodItem, weight: number) => {
  try {
    // 检查是否已存在该膳食类型的记录
    let existingRecord = getMealRecordByType(currentMealType.value)

    if (existingRecord) {
      // 如果已存在记录，直接添加食物到该记录
      await addFoodToMealRecord(existingRecord.id, food.id, weight)
    } else {
      // 如果不存在记录，先创建膳食记录，然后添加食物
      const mealRecordRequest: MealRecordRequest = {
        recordDate: selectedDate.value,
        mealType: currentMealType.value as any,
        mealItems: [{
          foodId: food.id,
          weight: weight
        }]
      }
      await createMealRecord(mealRecordRequest)
    }

    // 重新加载膳食记录
    await loadMealRecords()

  } catch (err) {
    console.error('添加食物失败:', err)
    alert('添加食物失败: ' + (err instanceof Error ? err.message : '未知错误'))
  }
}

const editMealItem = (recordId: number, item: MealItemResponse) => {
  const newWeight = prompt(`修改${item.foodName}的重量 (当前: ${item.weight}g):`, item.weight.toString())
  if (newWeight && !isNaN(Number(newWeight))) {
    updateItemWeight(recordId, item.id, Number(newWeight))
  }
}

const updateItemWeight = async (recordId: number, itemId: number, weight: number) => {
  try {
    await updateMealItemWeight(recordId, itemId, weight)
    await loadMealRecords() // 重新加载数据
  } catch (err) {
    console.error('更新重量失败:', err)
    alert('更新重量失败: ' + (err instanceof Error ? err.message : '未知错误'))
  }
}

const removeMealItem = async (recordId: number, itemId: number) => {
  if (confirm('确定要删除这个食物记录吗？')) {
    try {
      await removeFoodFromMealRecord(recordId, itemId)
      await loadMealRecords() // 重新加载数据
    } catch (err) {
      console.error('删除食物失败:', err)
      alert('删除食物失败: ' + (err instanceof Error ? err.message : '未知错误'))
    }
  }
}

// 营养统计方法
const getTotalNutrition = (nutrientType: string): string => {
  const total = mealRecords.value.reduce((sum, record) => {
    switch (nutrientType) {
      case 'energy':
        return sum + (record.totalEnergy || 0)
      case 'protein':
        return sum + (record.totalProtein || 0)
      case 'fat':
        return sum + (record.totalFat || 0)
      case 'carbohydrate':
        return sum + (record.totalCarbohydrate || 0)
      case 'dietaryFiber':
        return sum + (record.totalDietaryFiber || 0)
      default:
        return sum
    }
  }, 0)

  return total.toFixed(1)
}

const getNutritionPercentage = (nutrientType: string): number => {
  const current = parseFloat(getTotalNutrition(nutrientType))

  // 推荐每日摄入量 (可以根据用户信息调整)
  const recommendations: Record<string, number> = {
    protein: 60,      // 60g
    fat: 65,          // 65g
    carbohydrate: 300 // 300g
  }

  const recommended = recommendations[nutrientType] || 100
  return Math.min((current / recommended) * 100, 100)
}

const getMealEnergy = (mealType: string): string => {
  const record = getMealRecordByType(mealType)
  return record ? (record.totalEnergy || 0).toFixed(0) : '0'
}

// 生命周期
onMounted(() => {
  loadMealRecords()
})
</script>

<style scoped>
/* CSS变量定义 - 与首页保持一致 */
:root {
  --primary-color: #16a085;
  --primary-light: #1abc9c;
  --primary-dark: #138d75;
  --secondary-color: #3498db;
  --accent-color: #e74c3c;
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --text-light: #bdc3c7;
  --text-muted: #95a5a6;
  --background-light: #f8f9fa;
  --background-white: #ffffff;
  --border-color: #e9ecef;
  --border-light: #f1f3f4;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --primary-green: #16a085;
}

.meals-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
  overflow-x: hidden;
}

/* 背景装饰 - 与首页一致 */
.meals-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(22, 160, 133, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(26, 188, 156, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(52, 152, 219, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.main-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  padding-top: 6rem; /* 为顶部导航栏留出空间 */
}

/* 页面标题 - 优化视觉效果 */
.page-header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 3rem 2rem 2rem 2rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.8) 100%);
  border-radius: var(--radius-xl);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(135deg, #16a085 0%, #1abc9c 50%, #3498db 100%);
}

.page-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  color: #2c3e50;
  line-height: 1.2;
  position: relative;
  z-index: 1;
}

.title-icon {
  font-size: 2.2rem;
  /* {{ AURA-X: Modify - 移除渐变样式，显示原本的🍽️颜色. Approval: 寸止. }} */
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.page-subtitle {
  color: #7f8c8d;
  font-size: 1.1rem;
  margin: 0;
  line-height: 1.6;
  max-width: 500px;
  margin: 0 auto;
  font-weight: 400;
  opacity: 0.9;
}

/* 日期选择器 - 与首页卡片风格一致 */
.date-selector-section {
  margin-bottom: 3rem;
}

.date-selector-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.9) 100%);
  border-radius: var(--radius-xl);
  padding: 1.5rem 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: flex;
  flex-direction: column;
  gap: 1rem;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.date-selector-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(135deg, #16a085 0%, #1abc9c 50%, #3498db 100%);
}

.date-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.date-nav-btn {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 2px solid #e9ecef;
  border-radius: 50%;
  width: 46px;
  height: 46px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.2rem;
  color: #6c757d;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.08);
}

.date-nav-btn:hover {
  background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
  border-color: #16a085;
  color: white;
  transform: scale(1.05) translateY(-1px);
  box-shadow: 0 6px 20px rgba(22, 160, 133, 0.25);
}

.current-date {
  position: relative;
  display: flex;
  align-items: center;
}

.date-input {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.date-display {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  padding: 0.8rem 1.8rem;
  border-radius: var(--radius-lg);
  font-weight: 700;
  font-size: 1.1rem;
  min-width: 160px;
  text-align: center;
  box-shadow: 0 4px 16px rgba(44, 62, 80, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  letter-spacing: 0.5px;
}

.quick-date-buttons {
  display: flex;
  justify-content: center;
  gap: 0.75rem;
}

.quick-date-btn {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.8) 100%);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-md);
  padding: 0.75rem 1.25rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.95rem;
  color: #2c3e50;
  font-weight: 500;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(5px);
}

.quick-date-btn:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(22, 160, 133, 0.05) 100%);
  border-color: rgba(22, 160, 133, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(22, 160, 133, 0.15);
  color: #16a085;
}

.quick-date-btn.active {
  background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
  color: white;
  border-color: #16a085;
  box-shadow: 0 6px 20px rgba(22, 160, 133, 0.3);
}

/* 加载和错误状态 */
.loading-state, .error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.error-content h4 {
  color: #dc2626;
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
}

.error-content p {
  color: #6b7280;
  margin: 0 0 1rem 0;
}

.retry-btn {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.2s ease;
}

.retry-btn:hover {
  background: #2563eb;
}

/* 每日营养概览 */
.daily-overview-section {
  margin-bottom: 3rem;
}

.overview-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.9) 100%);
  border-radius: var(--radius-xl);
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.overview-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(135deg, #16a085 0%, #1abc9c 50%, #3498db 100%);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.overview-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.overview-icon {
  font-size: 1.75rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.overview-date {
  background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
  color: white;
  padding: 0.6rem 1.2rem;
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: 0.9rem;
  box-shadow: 0 2px 8px rgba(22, 160, 133, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.nutrition-overview {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 3rem;
  align-items: center;
}

.nutrition-circle-chart {
  display: flex;
  justify-content: center;
}

.chart-container {
  position: relative;
}

.energy-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  /* {{ AURA-X: Modify - 修复背景颜色，确保文字可见. Approval: 寸止. }} */
  background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 8px 20px rgba(22, 160, 133, 0.3);
  position: relative;
  overflow: hidden;
}

.energy-circle::before {
  content: '';
  position: absolute;
  top: 10px;
  left: 10px;
  right: 10px;
  bottom: 10px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.energy-value {
  font-size: 1.8rem;
  font-weight: 700;
  z-index: 1;
}

.energy-unit {
  font-size: 0.8rem;
  opacity: 0.9;
  z-index: 1;
}

.nutrition-details {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.nutrition-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.nutrition-bar {
  height: 8px;
  background: var(--border-light);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.bar-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.8s ease;
  position: relative;
}

.protein-bar {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.fat-bar {
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.carb-bar {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.nutrition-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nutrition-name {
  font-weight: 600;
  color: #34495e;
  font-size: 0.95rem;
}

.nutrition-amount {
  font-weight: 700;
  color: #2c3e50;
  font-size: 1rem;
  background: rgba(22, 160, 133, 0.1);
  padding: 0.2rem 0.6rem;
  border-radius: 8px;
  border: 1px solid rgba(22, 160, 133, 0.2);
}

/* {{ AURA-X: Modify - 重新设计为Material Design风格的膳食卡片. Approval: 寸止. }} */
/* 膳食记录网格 */
.meal-records-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
  gap: 1.5rem;
  padding: 0.5rem;
}

/* Material Design 膳食卡片 */
.meal-card {
  background: #ffffff;
  border-radius: 16px;
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.1);
  border: none;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  transform: translateZ(0);
}

.meal-card:hover {
  transform: translateY(-4px) translateZ(0);
  box-shadow:
    0 4px 8px rgba(0, 0, 0, 0.12),
    0 16px 32px rgba(0, 0, 0, 0.15);
}

.meal-card.has-records {
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.1),
    0 0 0 2px rgba(22, 160, 133, 0.1);
}

.meal-card.has-records:hover {
  box-shadow:
    0 4px 8px rgba(0, 0, 0, 0.12),
    0 16px 32px rgba(0, 0, 0, 0.15),
    0 0 0 2px rgba(22, 160, 133, 0.2);
}

/* Material Design 膳食卡片头部 */
.meal-card-header {
  position: relative;
  padding: 1.25rem 1.5rem;
  background: #ffffff;
  border-bottom: 1px solid #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.meal-header-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: #16a085;
}

.meal-breakfast .meal-header-background {
  background: linear-gradient(90deg, #ff9800 0%, #f57c00 100%);
}

.meal-lunch .meal-header-background {
  background: linear-gradient(90deg, #f44336 0%, #d32f2f 100%);
}

.meal-dinner .meal-header-background {
  background: linear-gradient(90deg, #9c27b0 0%, #7b1fa2 100%);
}

.meal-snack .meal-header-background {
  background: linear-gradient(90deg, #4caf50 0%, #388e3c 100%);
}

.meal-type-badge {
  width: 48px;
  height: 48px;
  background: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.1),
    0 4px 8px rgba(0, 0, 0, 0.1);
  border: 2px solid #16a085;
  flex-shrink: 0;
}

.meal-breakfast .meal-type-badge {
  border-color: #ff9800;
}

.meal-lunch .meal-type-badge {
  border-color: #f44336;
}

.meal-dinner .meal-type-badge {
  border-color: #9c27b0;
}

.meal-snack .meal-type-badge {
  border-color: #4caf50;
}

.meal-type-icon {
  font-size: 1.5rem;
}

.meal-header-content {
  margin-left: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
}

.meal-type-name {
  font-size: 1.375rem;
  font-weight: 600;
  color: #212121;
  margin: 0;
  letter-spacing: -0.01em;
}

.meal-breakfast .meal-type-name {
  color: #e65100;
}

.meal-lunch .meal-type-name {
  color: #c62828;
}

.meal-dinner .meal-type-name {
  color: #6a1b9a;
}

.meal-snack .meal-type-name {
  color: #2e7d32;
}

.meal-type-time {
  color: #757575;
  font-size: 0.875rem;
  font-weight: 500;
  background: #f5f5f5;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  display: inline-block;
}

.meal-energy-display {
  font-size: 1.125rem;
  font-weight: 600;
  margin-top: 0.25rem;
  padding: 0.375rem 0.875rem;
  border-radius: 20px;
  background: #f5f5f5;
  color: #424242;
  display: inline-block;
}

.meal-breakfast .meal-energy-display {
  color: #e65100;
  background: rgba(255, 152, 0, 0.1);
}

.meal-lunch .meal-energy-display {
  color: #c62828;
  background: rgba(244, 67, 54, 0.1);
}

.meal-dinner .meal-energy-display {
  color: #6a1b9a;
  background: rgba(156, 39, 176, 0.1);
}

.meal-snack .meal-energy-display {
  color: #2e7d32;
  background: rgba(76, 175, 80, 0.1);
}

.floating-add-btn {
  width: 40px;
  height: 40px;
  background: #16a085;
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 1.25rem;
  font-weight: 500;
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.2),
    0 4px 8px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.floating-add-btn:hover {
  transform: scale(1.1);
  box-shadow:
    0 4px 8px rgba(0, 0, 0, 0.25),
    0 8px 16px rgba(0, 0, 0, 0.15);
}

.floating-add-btn:active {
  transform: scale(0.95);
}

.meal-type-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 249, 250, 0.6) 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  backdrop-filter: blur(5px);
}

.meal-type-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 2rem;
  right: 2rem;
  height: 2px;
  background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
  opacity: 0.4;
}

.meal-type-info {
  display: flex;
  align-items: center;
  gap: 1.25rem;
}

.meal-type-icon {
  font-size: 1.75rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.meal-type-name {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  letter-spacing: -0.02em;
}

.meal-type-time {
  color: var(--text-secondary);
  font-size: 0.9rem;
  background: var(--border-light);
  padding: 0.4rem 0.9rem;
  border-radius: var(--radius-lg);
  font-weight: 500;
  border: 1px solid var(--border-color);
}

.add-food-btn {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: white;
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.4rem;
  font-weight: bold;
  box-shadow: var(--shadow-light);
  position: relative;
  overflow: hidden;
}

.add-food-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.add-food-btn:hover {
  transform: scale(1.15) rotate(90deg);
  box-shadow: var(--shadow-medium);
}

.add-food-btn:hover::before {
  opacity: 1;
}

/* Material Design 膳食条目网格 */
.meal-card-body {
  padding: 1rem 1.5rem;
  background: #ffffff;
}

.meal-items-grid {
  display: grid;
  gap: 0.75rem;
}

.meal-item-card {
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.meal-item-card:hover {
  border-color: #16a085;
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.1),
    0 4px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.food-name {
  font-weight: 600;
  color: #212121;
  font-size: 1rem;
  line-height: 1.4;
}

.item-actions {
  display: flex;
  gap: 0.25rem;
  flex-shrink: 0;
}

.item-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.weight-info {
  display: flex;
  align-items: center;
}

.weight-value {
  color: #757575;
  font-size: 0.875rem;
  font-weight: 500;
}

.energy-info {
  display: flex;
  align-items: center;
}

.energy-value {
  background: #fff3e0;
  color: #e65100;
  padding: 0.25rem 0.75rem;
  border-radius: 16px;
  font-size: 0.875rem;
  font-weight: 600;
}

.action-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 1rem;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-btn:hover {
  background: rgba(33, 150, 243, 0.1);
  transform: scale(1.1);
}

.remove-btn:hover {
  background: rgba(244, 67, 54, 0.1);
  transform: scale(1.1);
}

.empty-meal-state {
  text-align: center;
  padding: 3rem 2rem;
  color: #757575;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.6;
}

.empty-text {
  font-size: 1rem;
  margin-bottom: 1.5rem;
  color: #9e9e9e;
}

.add-first-btn {
  background: #16a085;
  color: white;
  border: none;
  border-radius: 24px;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.1),
    0 4px 8px rgba(0, 0, 0, 0.1);
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.add-first-btn:hover {
  background: #138d75;
  transform: translateY(-1px);
  box-shadow:
    0 4px 8px rgba(0, 0, 0, 0.15),
    0 8px 16px rgba(0, 0, 0, 0.1);
}

.btn-icon {
  font-size: 1rem;
  font-weight: bold;
}

/* Material Design 膳食营养汇总 */
.meal-card-footer {
  border-top: 1px solid #f0f0f0;
  padding: 1rem 1.5rem;
  background: #fafafa;
}

.nutrition-mini-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.mini-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 0.75rem 0.5rem;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.mini-stat:hover {
  border-color: #16a085;
  transform: translateY(-1px);
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.1),
    0 4px 8px rgba(0, 0, 0, 0.05);
}

.stat-label {
  font-size: 0.75rem;
  color: #757575;
  margin-bottom: 0.25rem;
  font-weight: 500;
}

.stat-value {
  font-size: 1rem;
  font-weight: 600;
  color: #212121;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: 1rem;
    padding-top: 5rem;
  }

  .page-title {
    font-size: 2rem;
  }

  .date-selector-card {
    padding: 1rem;
  }

  .date-controls {
    flex-direction: column;
    gap: 0.75rem;
  }

  .quick-date-buttons {
    order: -1;
  }

  .meal-type-header {
    padding: 1rem;
  }

  .meal-type-info {
    gap: 0.75rem;
  }

  .meal-type-name {
    font-size: 1.1rem;
  }

  .meal-items-list {
    padding: 0.75rem 1rem;
  }

  .meal-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.75rem;
  }

  .meal-item-info {
    width: 100%;
  }

  .meal-item-nutrition {
    margin: 0;
    width: 100%;
    justify-content: flex-start;
  }

  .meal-item-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .nutrition-summary {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .nutrition-item {
    padding: 0.5rem;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 1.75rem;
    flex-direction: column;
    gap: 0.25rem;
  }

  .title-icon {
    font-size: 1.5rem;
  }

  .date-nav-btn {
    width: 36px;
    height: 36px;
    font-size: 1rem;
  }

  .date-display {
    padding: 0.5rem 1rem;
    font-size: 1rem;
    min-width: 100px;
  }

  .quick-date-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
  }

  .meal-type-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .add-food-btn {
    align-self: flex-end;
  }

  .nutrition-summary {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .nutrition-item {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
  }

  .nutrition-label {
    margin-bottom: 0;
  }
}

/* 动画效果 */
.meal-type-card {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.meal-item {
  animation: fadeInScale 0.2s ease-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 滚动条样式 */
.meal-items-list::-webkit-scrollbar {
  width: 6px;
}

.meal-items-list::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.meal-items-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.meal-items-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>

<style scoped>
.page-container {
  min-height: 100vh;
  background: #f8fafc;
  padding: 2rem;
}

.page-header {
  text-align: center;
  margin-bottom: 2rem;
}

.page-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.page-header p {
  color: #64748b;
  font-size: 1.125rem;
  margin: 0;
}

.page-content {
  max-width: 600px;
  margin: 0 auto;
}

.placeholder-card {
  background: white;
  border-radius: 16px;
  padding: 3rem;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.placeholder-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.placeholder-card h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1rem 0;
}

.placeholder-card p {
  color: #64748b;
  margin: 0 0 1rem 0;
}

.placeholder-card ul {
  text-align: left;
  color: #64748b;
  margin: 0 0 2rem 0;
}

.placeholder-card li {
  margin: 0.5rem 0;
}

.status-badge {
  display: inline-block;
  background: #fef3c7;
  color: #d97706;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}
</style>

package com.example.meals.mapper;

import com.example.meals.entity.MealRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 膳食记录Mapper接口
 * {{ AURA-X: Add - 创建膳食记录Mapper接口. Approval: 寸止. }}
 */
@Mapper
public interface MealRecordMapper {

    /**
     * 插入膳食记录
     * @param mealRecord 膳食记录
     * @return 影响行数
     */
    int insert(MealRecord mealRecord);

    /**
     * 更新膳食记录
     * @param mealRecord 膳食记录
     * @return 影响行数
     */
    int update(MealRecord mealRecord);

    /**
     * 根据ID删除膳食记录
     * @param id 膳食记录ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据ID查询膳食记录
     * @param id 膳食记录ID
     * @return 膳食记录
     */
    MealRecord selectById(@Param("id") Long id);

    /**
     * 根据用户ID和日期查询膳食记录
     * @param userId 用户ID
     * @param recordDate 记录日期
     * @return 膳食记录列表
     */
    List<MealRecord> selectByUserIdAndDate(@Param("userId") Long userId, @Param("recordDate") LocalDate recordDate);

    /**
     * 根据用户ID和日期范围查询膳食记录
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 膳食记录列表
     */
    List<MealRecord> selectByUserIdAndDateRange(@Param("userId") Long userId, 
                                               @Param("startDate") LocalDate startDate, 
                                               @Param("endDate") LocalDate endDate);

    /**
     * 根据用户ID、日期和膳食类型查询膳食记录
     * @param userId 用户ID
     * @param recordDate 记录日期
     * @param mealType 膳食类型
     * @return 膳食记录
     */
    MealRecord selectByUserIdDateAndMealType(@Param("userId") Long userId, 
                                           @Param("recordDate") LocalDate recordDate, 
                                           @Param("mealType") String mealType);

    /**
     * 根据用户ID查询最近的膳食记录
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 膳食记录列表
     */
    List<MealRecord> selectRecentByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 更新膳食记录的营养总值
     * @param id 膳食记录ID
     * @param totalEnergy 总能量
     * @param totalProtein 总蛋白质
     * @param totalFat 总脂肪
     * @param totalCarbohydrate 总碳水化合物
     * @param totalDietaryFiber 总膳食纤维
     * @return 影响行数
     */
    int updateNutritionTotals(@Param("id") Long id,
                             @Param("totalEnergy") java.math.BigDecimal totalEnergy,
                             @Param("totalProtein") java.math.BigDecimal totalProtein,
                             @Param("totalFat") java.math.BigDecimal totalFat,
                             @Param("totalCarbohydrate") java.math.BigDecimal totalCarbohydrate,
                             @Param("totalDietaryFiber") java.math.BigDecimal totalDietaryFiber);

    /**
     * 统计用户膳食记录数量
     * @param userId 用户ID
     * @return 记录数量
     */
    int countByUserId(@Param("userId") Long userId);

    /**
     * 统计用户指定日期范围内的膳食记录数量
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 记录数量
     */
    int countByUserIdAndDateRange(@Param("userId") Long userId, 
                                 @Param("startDate") LocalDate startDate, 
                                 @Param("endDate") LocalDate endDate);
}

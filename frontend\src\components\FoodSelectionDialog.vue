<template>
  <!-- {{ AURA-X: Add - 创建食物选择对话框组件. Approval: 寸止. }} -->
  <div v-if="isVisible" class="dialog-overlay" @click="closeDialog">
    <div class="dialog-container" @click.stop>
      <div class="dialog-header">
        <h3 class="dialog-title">
          <span class="meal-icon">{{ mealTypeIcon }}</span>
          添加{{ mealTypeName }}食物
        </h3>
        <button @click="closeDialog" class="close-btn">✕</button>
      </div>

      <div class="dialog-content">
        <!-- 搜索区域 -->
        <div class="search-section">
          <div class="search-input-wrapper">
            <input
              v-model="searchQuery"
              @input="handleSearch"
              @keyup.enter="performSearch"
              type="text"
              placeholder="搜索食物名称..."
              class="search-input"
              ref="searchInputRef"
            />
            <button @click="performSearch" class="search-btn">
              🔍
            </button>
          </div>
        </div>

        <!-- 搜索结果 -->
        <div class="search-results-section">
          <!-- 加载状态 -->
          <div v-if="isSearching" class="loading-state">
            <div class="loading-spinner"></div>
            <p>正在搜索食物...</p>
          </div>

          <!-- 搜索结果 -->
          <div v-else-if="searchResults.length > 0" class="search-results">
            <div 
              v-for="food in searchResults" 
              :key="food.id"
              class="food-item"
              :class="{ selected: selectedFood?.id === food.id }"
              @click="selectFood(food)"
            >
              <div class="food-info">
                <h4 class="food-name">{{ food.foodName }}</h4>
                <p class="food-category">{{ food.category }}</p>
                <div class="food-nutrition">
                  <span class="nutrition-item">{{ food.energyKcal }}kcal/100g</span>
                  <span class="nutrition-item">蛋白质{{ formatNutritionValue(food.protein, 'g') }}</span>
                </div>
              </div>
              <div class="selection-indicator">
                <span v-if="selectedFood?.id === food.id" class="selected-icon">✓</span>
              </div>
            </div>
          </div>

          <!-- 无搜索结果 -->
          <div v-else-if="searchQuery && !isSearching" class="no-results">
            <div class="no-results-icon">🔍</div>
            <p>未找到相关食物</p>
            <p class="no-results-tip">请尝试其他关键词</p>
          </div>

          <!-- 初始状态 -->
          <div v-else class="search-placeholder">
            <div class="placeholder-icon">🥗</div>
            <p>请输入食物名称进行搜索</p>
          </div>
        </div>

        <!-- 重量输入 -->
        <div v-if="selectedFood" class="weight-section">
          <div class="weight-input-group">
            <label class="weight-label">食用重量：</label>
            <div class="weight-input-wrapper">
              <input
                v-model.number="foodWeight"
                type="number"
                min="1"
                max="10000"
                class="weight-input"
                placeholder="100"
              />
              <span class="weight-unit">克</span>
            </div>
          </div>
          
          <!-- 营养预览 -->
          <div class="nutrition-preview">
            <h4 class="preview-title">营养成分预览</h4>
            <div class="nutrition-grid">
              <div class="nutrition-item">
                <span class="nutrition-label">能量</span>
                <span class="nutrition-value">{{ calculateNutrition('energyKcal') }}kcal</span>
              </div>
              <div class="nutrition-item">
                <span class="nutrition-label">蛋白质</span>
                <span class="nutrition-value">{{ calculateNutrition('protein') }}g</span>
              </div>
              <div class="nutrition-item">
                <span class="nutrition-label">脂肪</span>
                <span class="nutrition-value">{{ calculateNutrition('fat') }}g</span>
              </div>
              <div class="nutrition-item">
                <span class="nutrition-label">碳水</span>
                <span class="nutrition-value">{{ calculateNutrition('cho') }}g</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="dialog-footer">
        <button @click="closeDialog" class="cancel-btn">取消</button>
        <button 
          @click="confirmSelection" 
          :disabled="!selectedFood || !isValidWeight"
          class="confirm-btn"
        >
          添加食物
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { searchFoods, type FoodItem } from '../utils/foodApi'
import { formatNutritionValue } from '../utils/mealRecordApi'

// Props
interface Props {
  isVisible: boolean
  mealType: string
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'close'): void
  (e: 'confirm', food: FoodItem, weight: number): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const searchQuery = ref('')
const searchResults = ref<FoodItem[]>([])
const selectedFood = ref<FoodItem | null>(null)
const foodWeight = ref(100)
const isSearching = ref(false)
const searchInputRef = ref<HTMLInputElement>()

// 计算属性
const mealTypeNames: Record<string, string> = {
  breakfast: '早餐',
  lunch: '午餐', 
  dinner: '晚餐',
  snack: '加餐'
}

const mealTypeIcons: Record<string, string> = {
  breakfast: '🌅',
  lunch: '☀️',
  dinner: '🌙', 
  snack: '🍎'
}

const mealTypeName = computed(() => mealTypeNames[props.mealType] || '膳食')
const mealTypeIcon = computed(() => mealTypeIcons[props.mealType] || '🍽️')

const isValidWeight = computed(() => {
  return foodWeight.value >= 1 && foodWeight.value <= 10000 && !isNaN(foodWeight.value)
})

// 方法
const closeDialog = () => {
  emit('close')
  resetDialog()
}

const resetDialog = () => {
  searchQuery.value = ''
  searchResults.value = []
  selectedFood.value = null
  foodWeight.value = 100
  isSearching.value = false
}

const handleSearch = () => {
  if (searchQuery.value.trim().length >= 1) {
    performSearch()
  } else {
    searchResults.value = []
  }
}

const performSearch = async () => {
  if (!searchQuery.value.trim()) return
  
  try {
    isSearching.value = true
    const results = await searchFoods(searchQuery.value.trim(), 20)
    searchResults.value = results
  } catch (error) {
    console.error('搜索食物失败:', error)
    searchResults.value = []
  } finally {
    isSearching.value = false
  }
}

const selectFood = (food: FoodItem) => {
  selectedFood.value = food
}

const calculateNutrition = (nutrient: keyof FoodItem): string => {
  if (!selectedFood.value || !isValidWeight.value) return '0'
  
  const value = selectedFood.value[nutrient] as number
  if (typeof value !== 'number') return '0'
  
  const calculated = (value * foodWeight.value) / 100
  return calculated.toFixed(1)
}

const confirmSelection = () => {
  if (selectedFood.value && isValidWeight.value) {
    emit('confirm', selectedFood.value, foodWeight.value)
    closeDialog()
  }
}

// 监听对话框显示状态
watch(() => props.isVisible, (visible) => {
  if (visible) {
    nextTick(() => {
      searchInputRef.value?.focus()
    })
  } else {
    resetDialog()
  }
})
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.dialog-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.dialog-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.meal-icon {
  font-size: 1.5rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #64748b;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.dialog-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* 搜索区域 */
.search-input-wrapper {
  display: flex;
  gap: 0.5rem;
}

.search-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
}

.search-btn {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 12px;
  padding: 0.75rem 1rem;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s ease;
}

.search-btn:hover {
  background: #2563eb;
}

/* 搜索结果区域 */
.search-results-section {
  min-height: 200px;
  max-height: 300px;
  overflow-y: auto;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #64748b;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.search-results {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.food-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.food-item:hover {
  border-color: #cbd5e1;
  background: #f8fafc;
}

.food-item.selected {
  border-color: #3b82f6;
  background: #eff6ff;
}

.food-info {
  flex: 1;
}

.food-name {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
}

.food-category {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0 0 0.5rem 0;
}

.food-nutrition {
  display: flex;
  gap: 1rem;
}

.nutrition-item {
  font-size: 0.8rem;
  color: #475569;
  background: #f1f5f9;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
}

.selection-indicator {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.selected-icon {
  color: #3b82f6;
  font-weight: bold;
  font-size: 1.2rem;
}

.no-results, .search-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #64748b;
  text-align: center;
}

.no-results-icon, .placeholder-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-results-tip {
  font-size: 0.875rem;
  color: #9ca3af;
  margin-top: 0.5rem;
}

/* 重量输入区域 */
.weight-section {
  border-top: 1px solid #e2e8f0;
  padding-top: 1.5rem;
}

.weight-input-group {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.weight-label {
  font-weight: 600;
  color: #374151;
  white-space: nowrap;
}

.weight-input-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.weight-input {
  width: 120px;
  padding: 0.5rem 0.75rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  text-align: center;
  transition: border-color 0.2s ease;
}

.weight-input:focus {
  outline: none;
  border-color: #3b82f6;
}

.weight-unit {
  color: #64748b;
  font-weight: 500;
}

/* 营养预览 */
.nutrition-preview {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1rem;
}

.preview-title {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 1rem 0;
}

.nutrition-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.nutrition-preview .nutrition-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 0.75rem;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.nutrition-label {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.nutrition-value {
  font-size: 0.875rem;
  font-weight: 700;
  color: #1e293b;
}

/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

.cancel-btn, .confirm-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.cancel-btn {
  background: #f1f5f9;
  color: #475569;
}

.cancel-btn:hover {
  background: #e2e8f0;
}

.confirm-btn {
  background: #10b981;
  color: white;
}

.confirm-btn:hover:not(:disabled) {
  background: #059669;
}

.confirm-btn:disabled {
  background: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .dialog-overlay {
    padding: 0.5rem;
  }

  .dialog-container {
    max-height: 95vh;
  }

  .dialog-header {
    padding: 1rem;
  }

  .dialog-content {
    padding: 1rem;
    gap: 1rem;
  }

  .search-input-wrapper {
    flex-direction: column;
  }

  .search-btn {
    width: 100%;
  }

  .weight-input-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .nutrition-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .dialog-footer {
    padding: 1rem;
    flex-direction: column;
  }

  .cancel-btn, .confirm-btn {
    width: 100%;
  }
}

/* 滚动条样式 */
.search-results-section::-webkit-scrollbar,
.dialog-content::-webkit-scrollbar {
  width: 6px;
}

.search-results-section::-webkit-scrollbar-track,
.dialog-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.search-results-section::-webkit-scrollbar-thumb,
.dialog-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.search-results-section::-webkit-scrollbar-thumb:hover,
.dialog-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>

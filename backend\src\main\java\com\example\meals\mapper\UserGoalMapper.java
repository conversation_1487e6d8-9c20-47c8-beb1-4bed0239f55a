package com.example.meals.mapper;

import com.example.meals.entity.UserGoal;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户健康目标数据访问接口
 */
@Mapper
public interface UserGoalMapper {

    /**
     * 根据用户ID查询目标列表
     */
    List<UserGoal> findByUserId(Long userId);

    /**
     * 根据用户ID和目标ID查询目标
     */
    UserGoal findByIdAndUserId(@Param("goalId") Long goalId, @Param("userId") Long userId);

    /**
     * 插入新的用户目标
     */
    int insert(UserGoal userGoal);

    /**
     * 更新用户目标
     */
    int update(UserGoal userGoal);

    /**
     * 更新目标进度
     */
    int updateProgress(@Param("id") Long id, @Param("userId") Long userId,
                      @Param("currentValue") Double currentValue,
                      @Param("progress") Double progress,
                      @Param("isCompleted") Boolean isCompleted);

    /**
     * 删除用户目标
     */
    int deleteByIdAndUserId(@Param("goalId") Long goalId, @Param("userId") Long userId);

    /**
     * 统计用户活跃目标数
     */
    int countActiveGoals(Long userId);

    /**
     * 统计用户已完成目标数
     */
    int countCompletedGoals(Long userId);

    /**
     * 统计用户总目标数
     */
    int countTotalGoals(Long userId);

    /**
     * 计算用户目标平均进度
     */
    Double getAverageProgress(Long userId);

    /**
     * 检查用户是否已有相同类型的活跃目标
     */
    int countActiveGoalsByType(@Param("userId") Long userId, @Param("goalType") String goalType);
}

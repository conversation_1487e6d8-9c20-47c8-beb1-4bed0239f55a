package com.example.meals.service.impl;

import com.example.meals.common.Result;
import com.example.meals.dto.HealthGoalResponse;
import com.example.meals.dto.UserGoalResponse;
import com.example.meals.dto.UserGoalStatsResponse;
import com.example.meals.dto.GoalHistoryResponse;
import com.example.meals.dto.request.HealthGoalRequest;
import com.example.meals.dto.request.CreateUserGoalRequest;
import com.example.meals.dto.request.UpdateUserGoalRequest;
import com.example.meals.entity.HealthGoal;
import com.example.meals.entity.UserGoal;
import com.example.meals.entity.UserGoalHistory;
import com.example.meals.mapper.HealthGoalMapper;
import com.example.meals.mapper.UserGoalMapper;
import com.example.meals.mapper.UserGoalHistoryMapper;
import com.example.meals.service.HealthGoalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 健康目标服务实现类
 */
@Service
public class HealthGoalServiceImpl implements HealthGoalService {
    
    @Autowired
    private HealthGoalMapper healthGoalMapper;

    @Autowired
    private UserGoalMapper userGoalMapper;

    @Autowired
    private UserGoalHistoryMapper userGoalHistoryMapper;
    
    @Override
    public Result<List<HealthGoalResponse>> getAllHealthGoals() {
        try {
            List<HealthGoal> goals = healthGoalMapper.selectAll();
            List<HealthGoalResponse> responses = goals.stream()
                    .map(HealthGoalResponse::new)
                    .collect(Collectors.toList());
            return Result.success(responses);
        } catch (Exception e) {
            return Result.error("获取健康目标列表失败");
        }
    }
    
    @Override
    public Result<List<HealthGoalResponse>> getEnabledHealthGoals() {
        try {
            List<HealthGoal> goals = healthGoalMapper.selectEnabled();
            List<HealthGoalResponse> responses = goals.stream()
                    .map(HealthGoalResponse::new)
                    .collect(Collectors.toList());
            return Result.success(responses);
        } catch (Exception e) {
            return Result.error("获取启用的健康目标列表失败");
        }
    }
    
    @Override
    public Result<HealthGoalResponse> getHealthGoalById(Long id) {
        try {
            HealthGoal goal = healthGoalMapper.selectById(id);
            if (goal == null) {
                return Result.notFound("健康目标不存在");
            }
            return Result.success(new HealthGoalResponse(goal));
        } catch (Exception e) {
            return Result.error("获取健康目标详情失败");
        }
    }
    
    @Override
    public Result<HealthGoalResponse> createHealthGoal(HealthGoalRequest request) {
        try {
            // 验证必填字段
            if (request.getCode() == null || request.getCode().trim().isEmpty()) {
                return Result.badRequest("目标代码不能为空");
            }
            if (request.getName() == null || request.getName().trim().isEmpty()) {
                return Result.badRequest("目标名称不能为空");
            }
            
            // 检查代码是否已存在
            if (healthGoalMapper.countByCode(request.getCode()) > 0) {
                return Result.badRequest("目标代码已存在");
            }
            
            // 创建实体
            HealthGoal goal = new HealthGoal();
            goal.setCode(request.getCode().trim());
            goal.setName(request.getName().trim());
            goal.setDescription(request.getDescription());
            goal.setSortOrder(request.getSortOrder() != null ? request.getSortOrder() : 
                             healthGoalMapper.getMaxSortOrder() + 1);
            goal.setStatus(request.getStatus() != null ? request.getStatus() : 1);
            
            // 插入数据库
            int result = healthGoalMapper.insert(goal);
            if (result > 0) {
                return Result.success(new HealthGoalResponse(goal));
            } else {
                return Result.error("创建健康目标失败");
            }
        } catch (Exception e) {
            return Result.error("创建健康目标失败");
        }
    }
    
    @Override
    public Result<HealthGoalResponse> updateHealthGoal(Long id, HealthGoalRequest request) {
        try {
            // 检查记录是否存在
            HealthGoal existing = healthGoalMapper.selectById(id);
            if (existing == null) {
                return Result.notFound("健康目标不存在");
            }
            
            // 验证必填字段
            if (request.getCode() == null || request.getCode().trim().isEmpty()) {
                return Result.badRequest("目标代码不能为空");
            }
            if (request.getName() == null || request.getName().trim().isEmpty()) {
                return Result.badRequest("目标名称不能为空");
            }
            
            // 检查代码是否已被其他记录使用
            if (healthGoalMapper.countByCodeExcludeId(request.getCode(), id) > 0) {
                return Result.badRequest("目标代码已存在");
            }
            
            // 更新实体
            existing.setCode(request.getCode().trim());
            existing.setName(request.getName().trim());
            existing.setDescription(request.getDescription());
            existing.setSortOrder(request.getSortOrder() != null ? request.getSortOrder() : existing.getSortOrder());
            existing.setStatus(request.getStatus() != null ? request.getStatus() : existing.getStatus());
            
            // 更新数据库
            int result = healthGoalMapper.update(existing);
            if (result > 0) {
                return Result.success(new HealthGoalResponse(existing));
            } else {
                return Result.error("更新健康目标失败");
            }
        } catch (Exception e) {
            return Result.error("更新健康目标失败");
        }
    }
    
    @Override
    public Result<Void> deleteHealthGoal(Long id) {
        try {
            // 检查记录是否存在
            HealthGoal existing = healthGoalMapper.selectById(id);
            if (existing == null) {
                return Result.notFound("健康目标不存在");
            }
            
            // 删除记录
            int result = healthGoalMapper.deleteById(id);
            if (result > 0) {
                return Result.success();
            } else {
                return Result.error("删除健康目标失败");
            }
        } catch (Exception e) {
            return Result.error("删除健康目标失败");
        }
    }

    // ==================== 用户健康目标管理方法实现 ====================

    @Override
    public Result<List<UserGoalResponse>> getUserGoals(Long userId) {
        try {
            List<UserGoal> userGoals = userGoalMapper.findByUserId(userId);
            List<UserGoalResponse> responses = userGoals.stream()
                .map(UserGoalResponse::new)
                .collect(java.util.stream.Collectors.toList());
            return Result.success(responses);
        } catch (Exception e) {
            return Result.error("获取用户目标失败: " + e.getMessage());
        }
    }

    @Override
    public Result<UserGoalResponse> createUserGoal(Long userId, CreateUserGoalRequest request) {
        try {
            // 验证必填字段
            if (request.getGoalCode() == null || request.getGoalCode().trim().isEmpty()) {
                return Result.badRequest("目标代码不能为空");
            }
            if (request.getTargetValue() == null || request.getTargetValue() <= 0) {
                return Result.badRequest("目标值必须大于0");
            }
            if (request.getDeadline() == null || request.getDeadline().trim().isEmpty()) {
                return Result.badRequest("截止日期不能为空");
            }

            // 验证目标代码是否存在
            HealthGoal healthGoal = healthGoalMapper.selectByCode(request.getGoalCode());
            if (healthGoal == null) {
                return Result.badRequest("目标代码不存在");
            }

            // 检查用户是否已有相同类型的活跃目标
            int existingCount = userGoalMapper.countActiveGoalsByType(userId, request.getGoalType());
            if (existingCount > 0) {
                return Result.badRequest("您已有相同类型的活跃目标，请先完成或停用现有目标");
            }

            // 创建UserGoal实体
            UserGoal userGoal = new UserGoal();
            userGoal.setUserId(userId);
            userGoal.setGoalCode(request.getGoalCode());
            userGoal.setGoalName(healthGoal.getName());
            userGoal.setGoalType(request.getGoalType());
            userGoal.setTargetValue(request.getTargetValue());
            userGoal.setCurrentValue(0.0);
            userGoal.setUnit(request.getUnit());
            userGoal.setDeadline(request.getDeadline());
            userGoal.setDescription(request.getDescription());
            userGoal.setIsActive(true);
            userGoal.setIsCompleted(false);
            userGoal.setProgress(0.0);

            // 保存到数据库
            int result = userGoalMapper.insert(userGoal);
            if (result > 0) {
                return Result.success(new UserGoalResponse(userGoal));
            } else {
                return Result.error("创建用户目标失败");
            }
        } catch (Exception e) {
            return Result.error("创建用户目标失败: " + e.getMessage());
        }
    }

    @Override
    public Result<UserGoalResponse> updateUserGoal(Long userId, Long goalId, UpdateUserGoalRequest request) {
        try {
            // 验证目标是否属于当前用户
            UserGoal userGoal = userGoalMapper.findByIdAndUserId(goalId, userId);
            if (userGoal == null) {
                return Result.notFound("目标不存在或不属于当前用户");
            }

            // 更新目标信息
            if (request.getTargetValue() != null && request.getTargetValue() > 0) {
                userGoal.setTargetValue(request.getTargetValue());
            }
            if (request.getCurrentValue() != null && request.getCurrentValue() >= 0) {
                userGoal.setCurrentValue(request.getCurrentValue());
            }
            if (request.getDeadline() != null && !request.getDeadline().trim().isEmpty()) {
                userGoal.setDeadline(request.getDeadline());
            }
            if (request.getDescription() != null) {
                userGoal.setDescription(request.getDescription());
            }
            if (request.getIsActive() != null) {
                userGoal.setIsActive(request.getIsActive());
            }

            // 重新计算进度
            userGoal.calculateProgress();

            // 保存到数据库
            int result = userGoalMapper.update(userGoal);
            if (result > 0) {
                return Result.success(new UserGoalResponse(userGoal));
            } else {
                return Result.error("更新用户目标失败");
            }
        } catch (Exception e) {
            return Result.error("更新用户目标失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> deleteUserGoal(Long userId, Long goalId) {
        try {
            // 验证目标是否属于当前用户
            UserGoal userGoal = userGoalMapper.findByIdAndUserId(goalId, userId);
            if (userGoal == null) {
                return Result.notFound("目标不存在或不属于当前用户");
            }

            // 删除相关历史记录
            userGoalHistoryMapper.deleteByGoalId(goalId, userId);

            // 删除目标
            int result = userGoalMapper.deleteByIdAndUserId(goalId, userId);
            if (result > 0) {
                return Result.success();
            } else {
                return Result.error("删除用户目标失败");
            }
        } catch (Exception e) {
            return Result.error("删除用户目标失败: " + e.getMessage());
        }
    }

    @Override
    public Result<UserGoalResponse> updateGoalProgress(Long userId, Long goalId, Double currentValue) {
        try {
            // 验证输入参数
            if (currentValue == null || currentValue < 0) {
                return Result.badRequest("当前值不能为空且必须大于等于0");
            }

            // 验证目标是否属于当前用户
            UserGoal userGoal = userGoalMapper.findByIdAndUserId(goalId, userId);
            if (userGoal == null) {
                return Result.notFound("目标不存在或不属于当前用户");
            }

            // 更新当前值
            userGoal.setCurrentValue(currentValue);

            // 重新计算进度
            userGoal.calculateProgress();

            // 更新数据库
            int result = userGoalMapper.updateProgress(goalId, userId, currentValue,
                userGoal.getProgress(), userGoal.getIsCompleted());

            if (result > 0) {
                // 记录历史
                UserGoalHistory history = new UserGoalHistory();
                history.setGoalId(goalId);
                history.setUserId(userId);
                history.setRecordDate(java.time.LocalDate.now());
                history.setValue(currentValue);
                history.setProgress(userGoal.getProgress());
                history.setNotes("进度更新");

                userGoalHistoryMapper.insertOrUpdate(history);

                return Result.success(new UserGoalResponse(userGoal));
            } else {
                return Result.error("更新目标进度失败");
            }
        } catch (Exception e) {
            return Result.error("更新目标进度失败: " + e.getMessage());
        }
    }

    @Override
    public Result<UserGoalStatsResponse> getUserGoalStats(Long userId) {
        try {
            // 统计活跃目标数
            int activeGoals = userGoalMapper.countActiveGoals(userId);

            // 统计已完成目标数
            int completedGoals = userGoalMapper.countCompletedGoals(userId);

            // 统计总目标数
            int totalGoals = userGoalMapper.countTotalGoals(userId);

            // 计算总体进度
            Double averageProgress = userGoalMapper.getAverageProgress(userId);
            double overallProgress = averageProgress != null ? averageProgress : 0.0;

            // 计算连续天数（简化版本，获取最近30天的记录天数）
            int currentStreak = userGoalHistoryMapper.getStreakDays(userId, 30);

            UserGoalStatsResponse stats = new UserGoalStatsResponse(
                activeGoals, completedGoals, totalGoals, overallProgress, currentStreak);
            return Result.success(stats);
        } catch (Exception e) {
            return Result.error("获取用户目标统计失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<GoalHistoryResponse>> getGoalHistory(Long userId, Long goalId, Integer days) {
        try {
            // 验证目标是否属于当前用户
            UserGoal userGoal = userGoalMapper.findByIdAndUserId(goalId, userId);
            if (userGoal == null) {
                return Result.notFound("目标不存在或不属于当前用户");
            }

            // 查询指定天数内的历史记录
            java.time.LocalDate endDate = java.time.LocalDate.now();
            java.time.LocalDate startDate = endDate.minusDays(days != null ? days : 30);

            List<UserGoalHistory> historyList = userGoalHistoryMapper.findByGoalIdAndDateRange(
                goalId, userId, startDate, endDate);

            // 转换为响应格式
            List<GoalHistoryResponse> responses = historyList.stream()
                .map(GoalHistoryResponse::new)
                .collect(java.util.stream.Collectors.toList());

            return Result.success(responses);
        } catch (Exception e) {
            return Result.error("获取目标历史失败: " + e.getMessage());
        }
    }
}

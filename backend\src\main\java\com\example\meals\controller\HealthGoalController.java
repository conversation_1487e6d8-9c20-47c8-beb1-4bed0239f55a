package com.example.meals.controller;

import com.example.meals.common.Result;
import com.example.meals.dto.HealthGoalResponse;
import com.example.meals.dto.UserGoalResponse;
import com.example.meals.dto.UserGoalStatsResponse;
import com.example.meals.dto.GoalHistoryResponse;
import com.example.meals.dto.request.HealthGoalRequest;
import com.example.meals.dto.request.CreateUserGoalRequest;
import com.example.meals.dto.request.UpdateUserGoalRequest;
import com.example.meals.dto.request.UpdateProgressRequest;
import com.example.meals.service.HealthGoalService;
import com.example.meals.utils.AuthUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 健康目标控制器
 */
@RestController
@RequestMapping("/api/health-goals")
public class HealthGoalController {
    
    @Autowired
    private HealthGoalService healthGoalService;
    
    /**
     * 获取所有健康目标（管理员功能）
     */
    @GetMapping("/admin/all")
    public Result<List<HealthGoalResponse>> getAllHealthGoals(HttpServletRequest request) {
        // 验证管理员权限
        Result<Void> authResult = AuthUtil.checkAdminPermission(request);
        if (authResult != null) {
            return Result.forbidden(authResult.getMessage());
        }
        
        return healthGoalService.getAllHealthGoals();
    }
    
    /**
     * 获取启用的健康目标（公开接口，用于用户选择）
     */
    @GetMapping("/enabled")
    public Result<List<HealthGoalResponse>> getEnabledHealthGoals() {
        return healthGoalService.getEnabledHealthGoals();
    }
    
    /**
     * 根据ID获取健康目标详情（管理员功能）
     */
    @GetMapping("/admin/{id}")
    public Result<HealthGoalResponse> getHealthGoalById(@PathVariable Long id, HttpServletRequest request) {
        // 验证管理员权限
        Result<Void> authResult = AuthUtil.checkAdminPermission(request);
        if (authResult != null) {
            return Result.forbidden(authResult.getMessage());
        }
        
        return healthGoalService.getHealthGoalById(id);
    }
    
    /**
     * 创建健康目标（管理员功能）
     */
    @PostMapping("/admin/create")
    public Result<HealthGoalResponse> createHealthGoal(
            @RequestBody HealthGoalRequest request, 
            HttpServletRequest httpRequest) {
        // 验证管理员权限
        Result<Void> authResult = AuthUtil.checkAdminPermission(httpRequest);
        if (authResult != null) {
            return Result.forbidden(authResult.getMessage());
        }
        
        return healthGoalService.createHealthGoal(request);
    }
    
    /**
     * 更新健康目标（管理员功能）
     */
    @PutMapping("/admin/{id}")
    public Result<HealthGoalResponse> updateHealthGoal(
            @PathVariable Long id,
            @RequestBody HealthGoalRequest request, 
            HttpServletRequest httpRequest) {
        // 验证管理员权限
        Result<Void> authResult = AuthUtil.checkAdminPermission(httpRequest);
        if (authResult != null) {
            return Result.forbidden(authResult.getMessage());
        }
        
        return healthGoalService.updateHealthGoal(id, request);
    }
    
    /**
     * 删除健康目标（管理员功能）
     */
    @DeleteMapping("/admin/{id}")
    public Result<Void> deleteHealthGoal(@PathVariable Long id, HttpServletRequest request) {
        // 验证管理员权限
        Result<Void> authResult = AuthUtil.checkAdminPermission(request);
        if (authResult != null) {
            return Result.forbidden(authResult.getMessage());
        }
        
        return healthGoalService.deleteHealthGoal(id);
    }

    // ==================== 用户健康目标管理接口 ====================

    /**
     * 获取当前用户的健康目标列表
     */
    @GetMapping("/user/goals")
    public Result<List<UserGoalResponse>> getUserGoals(HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            return Result.unauthorized("用户未登录");
        }

        return healthGoalService.getUserGoals(userId);
    }

    /**
     * 创建用户健康目标
     */
    @PostMapping("/user/goals")
    public Result<UserGoalResponse> createUserGoal(
            @RequestBody CreateUserGoalRequest request,
            HttpServletRequest httpRequest) {
        Long userId = (Long) httpRequest.getAttribute("userId");
        if (userId == null) {
            return Result.unauthorized("用户未登录");
        }

        return healthGoalService.createUserGoal(userId, request);
    }

    /**
     * 更新用户健康目标
     */
    @PutMapping("/user/goals/{goalId}")
    public Result<UserGoalResponse> updateUserGoal(
            @PathVariable Long goalId,
            @RequestBody UpdateUserGoalRequest request,
            HttpServletRequest httpRequest) {
        Long userId = (Long) httpRequest.getAttribute("userId");
        if (userId == null) {
            return Result.unauthorized("用户未登录");
        }

        return healthGoalService.updateUserGoal(userId, goalId, request);
    }

    /**
     * 删除用户健康目标
     */
    @DeleteMapping("/user/goals/{goalId}")
    public Result<Void> deleteUserGoal(
            @PathVariable Long goalId,
            HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            return Result.unauthorized("用户未登录");
        }

        return healthGoalService.deleteUserGoal(userId, goalId);
    }

    /**
     * 更新用户健康目标进度
     */
    @PutMapping("/user/goals/{goalId}/progress")
    public Result<UserGoalResponse> updateGoalProgress(
            @PathVariable Long goalId,
            @RequestBody UpdateProgressRequest request,
            HttpServletRequest httpRequest) {
        Long userId = (Long) httpRequest.getAttribute("userId");
        if (userId == null) {
            return Result.unauthorized("用户未登录");
        }

        return healthGoalService.updateGoalProgress(userId, goalId, request.getCurrentValue());
    }

    /**
     * 获取用户健康目标统计数据
     */
    @GetMapping("/user/stats")
    public Result<UserGoalStatsResponse> getUserGoalStats(HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            return Result.unauthorized("用户未登录");
        }

        return healthGoalService.getUserGoalStats(userId);
    }

    /**
     * 获取用户健康目标历史记录
     */
    @GetMapping("/user/goals/{goalId}/history")
    public Result<List<GoalHistoryResponse>> getGoalHistory(
            @PathVariable Long goalId,
            @RequestParam(defaultValue = "30") Integer days,
            HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            return Result.unauthorized("用户未登录");
        }

        return healthGoalService.getGoalHistory(userId, goalId, days);
    }
}

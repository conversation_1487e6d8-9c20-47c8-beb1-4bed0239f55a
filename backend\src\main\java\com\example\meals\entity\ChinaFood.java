package com.example.meals.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 中国食物成分表实体类
 * 基于《中国食物成分表标准版(第6版)》
 */
public class ChinaFood {

    private Long id;
    
    /**
     * 食物代码
     */
    private String foodCode;
    
    /**
     * 食物名称
     */
    private String foodName;
    
    /**
     * 可食部分(%)
     */
    private BigDecimal edible;
    
    /**
     * 水分(g)
     */
    private BigDecimal water;
    
    /**
     * 能量(kcal)
     */
    private Integer energyKcal;
    
    /**
     * 能量(kJ)
     */
    private Integer energyKj;
    
    /**
     * 蛋白质(g)
     */
    private BigDecimal protein;
    
    /**
     * 脂肪(g)
     */
    private BigDecimal fat;
    
    /**
     * 碳水化合物(g)
     */
    private BigDecimal cho;
    
    /**
     * 膳食纤维(g)
     */
    private BigDecimal dietaryFiber;
    
    /**
     * 胆固醇(mg)
     */
    private BigDecimal cholesterol;
    
    /**
     * 灰分(g)
     */
    private BigDecimal ash;
    
    /**
     * 维生素A(μg)
     */
    private BigDecimal vitaminA;
    
    /**
     * 胡萝卜素(μg)
     */
    private BigDecimal carotene;
    
    /**
     * 视黄醇(μg)
     */
    private BigDecimal retinol;
    
    /**
     * 硫胺素(mg)
     */
    private BigDecimal thiamin;
    
    /**
     * 核黄素(mg)
     */
    private BigDecimal riboflavin;
    
    /**
     * 尼克酸(mg)
     */
    private BigDecimal niacin;
    
    /**
     * 维生素C(mg)
     */
    private BigDecimal vitaminC;
    
    /**
     * 维生素E总量(mg)
     */
    private BigDecimal vitaminETotal;
    
    /**
     * α-维生素E(mg)
     */
    private BigDecimal vitaminE1;
    
    /**
     * β-维生素E(mg)
     */
    private BigDecimal vitaminE2;
    
    /**
     * γ-维生素E(mg)
     */
    private BigDecimal vitaminE3;
    
    /**
     * 钙(mg)
     */
    private BigDecimal ca;
    
    /**
     * 磷(mg)
     */
    private BigDecimal p;
    
    /**
     * 钾(mg)
     */
    private BigDecimal k;
    
    /**
     * 钠(mg)
     */
    private BigDecimal na;
    
    /**
     * 镁(mg)
     */
    private BigDecimal mg;
    
    /**
     * 铁(mg)
     */
    private BigDecimal fe;
    
    /**
     * 锌(mg)
     */
    private BigDecimal zn;
    
    /**
     * 硒(μg)
     */
    private BigDecimal se;
    
    /**
     * 铜(mg)
     */
    private BigDecimal cu;
    
    /**
     * 锰(mg)
     */
    private BigDecimal mn;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    // Getter and Setter methods
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getFoodCode() { return foodCode; }
    public void setFoodCode(String foodCode) { this.foodCode = foodCode; }

    public String getFoodName() { return foodName; }
    public void setFoodName(String foodName) { this.foodName = foodName; }

    public BigDecimal getEdible() { return edible; }
    public void setEdible(BigDecimal edible) { this.edible = edible; }

    public BigDecimal getWater() { return water; }
    public void setWater(BigDecimal water) { this.water = water; }

    public Integer getEnergyKcal() { return energyKcal; }
    public void setEnergyKcal(Integer energyKcal) { this.energyKcal = energyKcal; }

    public Integer getEnergyKj() { return energyKj; }
    public void setEnergyKj(Integer energyKj) { this.energyKj = energyKj; }

    public BigDecimal getProtein() { return protein; }
    public void setProtein(BigDecimal protein) { this.protein = protein; }

    public BigDecimal getFat() { return fat; }
    public void setFat(BigDecimal fat) { this.fat = fat; }

    public BigDecimal getCho() { return cho; }
    public void setCho(BigDecimal cho) { this.cho = cho; }

    public BigDecimal getDietaryFiber() { return dietaryFiber; }
    public void setDietaryFiber(BigDecimal dietaryFiber) { this.dietaryFiber = dietaryFiber; }

    public BigDecimal getCholesterol() { return cholesterol; }
    public void setCholesterol(BigDecimal cholesterol) { this.cholesterol = cholesterol; }

    public BigDecimal getAsh() { return ash; }
    public void setAsh(BigDecimal ash) { this.ash = ash; }

    public BigDecimal getVitaminA() { return vitaminA; }
    public void setVitaminA(BigDecimal vitaminA) { this.vitaminA = vitaminA; }

    public BigDecimal getCarotene() { return carotene; }
    public void setCarotene(BigDecimal carotene) { this.carotene = carotene; }

    public BigDecimal getRetinol() { return retinol; }
    public void setRetinol(BigDecimal retinol) { this.retinol = retinol; }

    public BigDecimal getThiamin() { return thiamin; }
    public void setThiamin(BigDecimal thiamin) { this.thiamin = thiamin; }

    public BigDecimal getRiboflavin() { return riboflavin; }
    public void setRiboflavin(BigDecimal riboflavin) { this.riboflavin = riboflavin; }

    public BigDecimal getNiacin() { return niacin; }
    public void setNiacin(BigDecimal niacin) { this.niacin = niacin; }

    public BigDecimal getVitaminC() { return vitaminC; }
    public void setVitaminC(BigDecimal vitaminC) { this.vitaminC = vitaminC; }

    public BigDecimal getVitaminETotal() { return vitaminETotal; }
    public void setVitaminETotal(BigDecimal vitaminETotal) { this.vitaminETotal = vitaminETotal; }

    public BigDecimal getVitaminE1() { return vitaminE1; }
    public void setVitaminE1(BigDecimal vitaminE1) { this.vitaminE1 = vitaminE1; }

    public BigDecimal getVitaminE2() { return vitaminE2; }
    public void setVitaminE2(BigDecimal vitaminE2) { this.vitaminE2 = vitaminE2; }

    public BigDecimal getVitaminE3() { return vitaminE3; }
    public void setVitaminE3(BigDecimal vitaminE3) { this.vitaminE3 = vitaminE3; }

    public BigDecimal getCa() { return ca; }
    public void setCa(BigDecimal ca) { this.ca = ca; }

    public BigDecimal getP() { return p; }
    public void setP(BigDecimal p) { this.p = p; }

    public BigDecimal getK() { return k; }
    public void setK(BigDecimal k) { this.k = k; }

    public BigDecimal getNa() { return na; }
    public void setNa(BigDecimal na) { this.na = na; }

    public BigDecimal getMg() { return mg; }
    public void setMg(BigDecimal mg) { this.mg = mg; }

    public BigDecimal getFe() { return fe; }
    public void setFe(BigDecimal fe) { this.fe = fe; }

    public BigDecimal getZn() { return zn; }
    public void setZn(BigDecimal zn) { this.zn = zn; }

    public BigDecimal getSe() { return se; }
    public void setSe(BigDecimal se) { this.se = se; }

    public BigDecimal getCu() { return cu; }
    public void setCu(BigDecimal cu) { this.cu = cu; }

    public BigDecimal getMn() { return mn; }
    public void setMn(BigDecimal mn) { this.mn = mn; }

    public String getRemark() { return remark; }
    public void setRemark(String remark) { this.remark = remark; }

    public LocalDateTime getCreateTime() { return createTime; }
    public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }

    public LocalDateTime getUpdateTime() { return updateTime; }
    public void setUpdateTime(LocalDateTime updateTime) { this.updateTime = updateTime; }
}

package com.example.meals.mapper;

import com.example.meals.entity.MealItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 膳食条目Mapper接口
 * {{ AURA-X: Add - 创建膳食条目Mapper接口. Approval: 寸止. }}
 */
@Mapper
public interface MealItemMapper {

    /**
     * 插入膳食条目
     * @param mealItem 膳食条目
     * @return 影响行数
     */
    int insert(MealItem mealItem);

    /**
     * 批量插入膳食条目
     * @param mealItems 膳食条目列表
     * @return 影响行数
     */
    int batchInsert(@Param("mealItems") List<MealItem> mealItems);

    /**
     * 更新膳食条目
     * @param mealItem 膳食条目
     * @return 影响行数
     */
    int update(MealItem mealItem);

    /**
     * 根据ID删除膳食条目
     * @param id 膳食条目ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据膳食记录ID删除所有膳食条目
     * @param mealRecordId 膳食记录ID
     * @return 影响行数
     */
    int deleteByMealRecordId(@Param("mealRecordId") Long mealRecordId);

    /**
     * 根据ID查询膳食条目
     * @param id 膳食条目ID
     * @return 膳食条目
     */
    MealItem selectById(@Param("id") Long id);

    /**
     * 根据膳食记录ID查询膳食条目列表
     * @param mealRecordId 膳食记录ID
     * @return 膳食条目列表
     */
    List<MealItem> selectByMealRecordId(@Param("mealRecordId") Long mealRecordId);

    /**
     * 根据膳食记录ID列表查询膳食条目列表
     * @param mealRecordIds 膳食记录ID列表
     * @return 膳食条目列表
     */
    List<MealItem> selectByMealRecordIds(@Param("mealRecordIds") List<Long> mealRecordIds);

    /**
     * 根据食物ID查询膳食条目列表
     * @param foodId 食物ID
     * @param limit 限制数量
     * @return 膳食条目列表
     */
    List<MealItem> selectByFoodId(@Param("foodId") Long foodId, @Param("limit") Integer limit);

    /**
     * 统计膳食记录的条目数量
     * @param mealRecordId 膳食记录ID
     * @return 条目数量
     */
    int countByMealRecordId(@Param("mealRecordId") Long mealRecordId);

    /**
     * 计算膳食记录的营养总值
     * @param mealRecordId 膳食记录ID
     * @return 营养总值Map，包含totalEnergy、totalProtein、totalFat、totalCarbohydrate、totalDietaryFiber
     */
    java.util.Map<String, java.math.BigDecimal> calculateNutritionTotals(@Param("mealRecordId") Long mealRecordId);
}

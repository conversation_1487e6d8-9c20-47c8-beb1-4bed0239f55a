package com.example.meals.dto;

import com.example.meals.entity.UserGoal;
import java.time.LocalDateTime;

/**
 * 用户健康目标响应DTO
 */
public class UserGoalResponse {
    
    private Long id;
    private Long userId;
    private String goalCode;
    private String goalName;
    private String goalType;
    private Double targetValue;
    private Double currentValue;
    private String unit;
    private String deadline;
    private String description;
    private Boolean isActive;
    private Boolean isCompleted;
    private Double progress;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 构造函数
    public UserGoalResponse() {}
    
    public UserGoalResponse(UserGoal userGoal) {
        this.id = userGoal.getId();
        this.userId = userGoal.getUserId();
        this.goalCode = userGoal.getGoalCode();
        this.goalName = userGoal.getGoalName();
        this.goalType = userGoal.getGoalType();
        this.targetValue = userGoal.getTargetValue();
        this.currentValue = userGoal.getCurrentValue();
        this.unit = userGoal.getUnit();
        this.deadline = userGoal.getDeadline();
        this.description = userGoal.getDescription();
        this.isActive = userGoal.getIsActive();
        this.isCompleted = userGoal.getIsCompleted();
        this.progress = userGoal.getProgress();
        this.createdAt = userGoal.getCreatedAt();
        this.updatedAt = userGoal.getUpdatedAt();
    }
    
    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getGoalCode() {
        return goalCode;
    }
    
    public void setGoalCode(String goalCode) {
        this.goalCode = goalCode;
    }
    
    public String getGoalName() {
        return goalName;
    }
    
    public void setGoalName(String goalName) {
        this.goalName = goalName;
    }
    
    public String getGoalType() {
        return goalType;
    }
    
    public void setGoalType(String goalType) {
        this.goalType = goalType;
    }
    
    public Double getTargetValue() {
        return targetValue;
    }
    
    public void setTargetValue(Double targetValue) {
        this.targetValue = targetValue;
    }
    
    public Double getCurrentValue() {
        return currentValue;
    }
    
    public void setCurrentValue(Double currentValue) {
        this.currentValue = currentValue;
    }
    
    public String getUnit() {
        return unit;
    }
    
    public void setUnit(String unit) {
        this.unit = unit;
    }
    
    public String getDeadline() {
        return deadline;
    }
    
    public void setDeadline(String deadline) {
        this.deadline = deadline;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    public Boolean getIsCompleted() {
        return isCompleted;
    }
    
    public void setIsCompleted(Boolean isCompleted) {
        this.isCompleted = isCompleted;
    }
    
    public Double getProgress() {
        return progress;
    }
    
    public void setProgress(Double progress) {
        this.progress = progress;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    @Override
    public String toString() {
        return "UserGoalResponse{" +
                "id=" + id +
                ", userId=" + userId +
                ", goalCode='" + goalCode + '\'' +
                ", goalName='" + goalName + '\'' +
                ", goalType='" + goalType + '\'' +
                ", targetValue=" + targetValue +
                ", currentValue=" + currentValue +
                ", unit='" + unit + '\'' +
                ", deadline='" + deadline + '\'' +
                ", description='" + description + '\'' +
                ", isActive=" + isActive +
                ", isCompleted=" + isCompleted +
                ", progress=" + progress +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}

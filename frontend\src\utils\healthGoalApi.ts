/**
 * 健康目标API工具
 * 提供健康目标相关的API调用方法
 */

import { authFetch } from './auth'

const API_BASE_URL = 'http://localhost:8080/api/health-goals'

// 类型定义
export interface HealthGoalOption {
  id: number
  code: string
  name: string
  description?: string
  sortOrder: number
  status: number
  statusName: string
  createTime: string
  updateTime: string
}

export interface UserGoal {
  id: number
  userId: number
  goalCode: string
  goalName: string
  goalType: 'calories' | 'weight' | 'exercise' | 'nutrition' | 'water' | 'sleep'
  targetValue: number
  currentValue: number
  unit: string
  deadline: string
  description?: string
  isActive: boolean
  isCompleted: boolean
  progress: number
  createdAt: string
  updatedAt: string
  // 前端计算的属性
  isOverdue?: boolean
}

export interface CreateUserGoalRequest {
  goalCode: string
  goalType: string
  targetValue: number
  unit: string
  deadline: string
  description?: string
}

export interface UpdateUserGoalRequest {
  targetValue?: number
  currentValue?: number
  deadline?: string
  description?: string
  isActive?: boolean
}

export interface ApiResponse<T> {
  success: boolean
  message: string
  data?: T
}

// 获取启用的健康目标选项（用于用户选择）
export async function getEnabledHealthGoals(): Promise<ApiResponse<HealthGoalOption[]>> {
  try {
    const response = await fetch(`${API_BASE_URL}/enabled`)
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const result = await response.json()
    return result
  } catch (error) {
    console.error('获取健康目标选项失败:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : '网络请求失败'
    }
  }
}

// 获取用户的健康目标列表
export async function getUserGoals(): Promise<ApiResponse<UserGoal[]>> {
  try {
    const response = await authFetch(`${API_BASE_URL}/user/goals`)
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const result = await response.json()
    return result
  } catch (error) {
    console.error('获取用户目标失败:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : '网络请求失败'
    }
  }
}

// 创建用户健康目标
export async function createUserGoal(goalData: CreateUserGoalRequest): Promise<ApiResponse<UserGoal>> {
  try {
    const response = await authFetch(`${API_BASE_URL}/user/goals`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(goalData)
    })
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const result = await response.json()
    return result
  } catch (error) {
    console.error('创建目标失败:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : '网络请求失败'
    }
  }
}

// 更新用户健康目标
export async function updateUserGoal(goalId: number, goalData: UpdateUserGoalRequest): Promise<ApiResponse<UserGoal>> {
  try {
    const response = await authFetch(`${API_BASE_URL}/user/goals/${goalId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(goalData)
    })
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const result = await response.json()
    return result
  } catch (error) {
    console.error('更新目标失败:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : '网络请求失败'
    }
  }
}

// 删除用户健康目标
export async function deleteUserGoal(goalId: number): Promise<ApiResponse<void>> {
  try {
    const response = await authFetch(`${API_BASE_URL}/user/goals/${goalId}`, {
      method: 'DELETE'
    })
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const result = await response.json()
    return result
  } catch (error) {
    console.error('删除目标失败:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : '网络请求失败'
    }
  }
}

// 更新目标进度
export async function updateGoalProgress(goalId: number, currentValue: number): Promise<ApiResponse<UserGoal>> {
  try {
    const response = await authFetch(`${API_BASE_URL}/user/goals/${goalId}/progress`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ currentValue })
    })
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const result = await response.json()
    return result
  } catch (error) {
    console.error('更新进度失败:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : '网络请求失败'
    }
  }
}

// 获取目标统计数据
export async function getGoalStats(): Promise<ApiResponse<{
  activeGoals: number
  completedGoals: number
  totalGoals: number
  overallProgress: number
  currentStreak: number
}>> {
  try {
    const response = await authFetch(`${API_BASE_URL}/user/stats`)
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const result = await response.json()
    return result
  } catch (error) {
    console.error('获取目标统计失败:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : '网络请求失败'
    }
  }
}

// 获取目标历史记录
export async function getGoalHistory(goalId: number, days: number = 30): Promise<ApiResponse<{
  date: string
  value: number
  progress: number
}[]>> {
  try {
    const response = await authFetch(`${API_BASE_URL}/user/goals/${goalId}/history?days=${days}`)
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const result = await response.json()
    return result
  } catch (error) {
    console.error('获取目标历史失败:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : '网络请求失败'
    }
  }
}

// 工具函数：计算目标进度
export function calculateProgress(currentValue: number, targetValue: number, goalType: string): number {
  if (targetValue === 0) return 0
  
  let progress = 0
  
  switch (goalType) {
    case 'weight':
      // 体重目标：假设是减重，进度 = (初始值 - 当前值) / (初始值 - 目标值) * 100
      // 这里简化处理，实际需要记录初始体重
      progress = Math.min((currentValue / targetValue) * 100, 100)
      break
    case 'calories':
    case 'exercise':
    case 'water':
    case 'sleep':
    case 'nutrition':
    default:
      // 其他目标：进度 = 当前值 / 目标值 * 100
      progress = Math.min((currentValue / targetValue) * 100, 100)
      break
  }
  
  return Math.max(0, progress)
}

// 工具函数：格式化目标值显示
export function formatGoalValue(value: number, unit: string): string {
  if (value >= 1000 && (unit === 'kcal' || unit === '步' || unit === 'ml')) {
    return `${(value / 1000).toFixed(1)}k ${unit}`
  }
  return `${value} ${unit}`
}

// 工具函数：判断目标是否过期
export function isGoalOverdue(deadline: string): boolean {
  const deadlineDate = new Date(deadline)
  const now = new Date()
  return deadlineDate < now
}

// 工具函数：获取目标类型的默认单位
export function getDefaultUnit(goalType: string): string {
  const units = {
    calories: 'kcal',
    weight: 'kg',
    exercise: '分钟',
    nutrition: 'g',
    water: 'ml',
    sleep: '小时'
  }
  return units[goalType as keyof typeof units] || ''
}

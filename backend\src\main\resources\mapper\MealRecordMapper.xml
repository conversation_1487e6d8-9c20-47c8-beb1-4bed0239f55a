<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.meals.mapper.MealRecordMapper">

    <!-- {{ AURA-X: Add - 创建膳食记录MyBatis映射文件. Approval: 寸止. }} -->
    <!-- 结果映射 -->
    <resultMap id="MealRecordResultMap" type="com.example.meals.entity.MealRecord">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="recordDate" column="record_date"/>
        <result property="mealType" column="meal_type"/>
        <result property="totalEnergy" column="total_energy"/>
        <result property="totalProtein" column="total_protein"/>
        <result property="totalFat" column="total_fat"/>
        <result property="totalCarbohydrate" column="total_carbohydrate"/>
        <result property="totalDietaryFiber" column="total_dietary_fiber"/>
        <result property="notes" column="notes"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- 插入膳食记录 -->
    <insert id="insert" parameterType="com.example.meals.entity.MealRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO meal_record (
            user_id, record_date, meal_type, total_energy, total_protein, 
            total_fat, total_carbohydrate, total_dietary_fiber, notes
        ) VALUES (
            #{userId}, #{recordDate}, #{mealType}, #{totalEnergy}, #{totalProtein},
            #{totalFat}, #{totalCarbohydrate}, #{totalDietaryFiber}, #{notes}
        )
    </insert>

    <!-- 更新膳食记录 -->
    <update id="update" parameterType="com.example.meals.entity.MealRecord">
        UPDATE meal_record SET
            meal_type = #{mealType},
            total_energy = #{totalEnergy},
            total_protein = #{totalProtein},
            total_fat = #{totalFat},
            total_carbohydrate = #{totalCarbohydrate},
            total_dietary_fiber = #{totalDietaryFiber},
            notes = #{notes}
        WHERE id = #{id}
    </update>

    <!-- 更新营养总值 -->
    <update id="updateNutritionTotals">
        UPDATE meal_record SET
            total_energy = #{totalEnergy},
            total_protein = #{totalProtein},
            total_fat = #{totalFat},
            total_carbohydrate = #{totalCarbohydrate},
            total_dietary_fiber = #{totalDietaryFiber}
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除膳食记录 -->
    <delete id="deleteById" parameterType="long">
        DELETE FROM meal_record WHERE id = #{id}
    </delete>

    <!-- 根据ID查询膳食记录 -->
    <select id="selectById" parameterType="long" resultMap="MealRecordResultMap">
        SELECT * FROM meal_record WHERE id = #{id}
    </select>

    <!-- 根据用户ID和日期查询膳食记录 -->
    <select id="selectByUserIdAndDate" resultMap="MealRecordResultMap">
        SELECT * FROM meal_record 
        WHERE user_id = #{userId} AND record_date = #{recordDate}
        ORDER BY 
            CASE meal_type 
                WHEN 'breakfast' THEN 1
                WHEN 'lunch' THEN 2
                WHEN 'dinner' THEN 3
                WHEN 'snack' THEN 4
                ELSE 5
            END,
            create_time ASC
    </select>

    <!-- 根据用户ID和日期范围查询膳食记录 -->
    <select id="selectByUserIdAndDateRange" resultMap="MealRecordResultMap">
        SELECT * FROM meal_record 
        WHERE user_id = #{userId} 
        AND record_date BETWEEN #{startDate} AND #{endDate}
        ORDER BY record_date DESC, 
            CASE meal_type 
                WHEN 'breakfast' THEN 1
                WHEN 'lunch' THEN 2
                WHEN 'dinner' THEN 3
                WHEN 'snack' THEN 4
                ELSE 5
            END,
            create_time ASC
    </select>

    <!-- 根据用户ID、日期和膳食类型查询膳食记录 -->
    <select id="selectByUserIdDateAndMealType" resultMap="MealRecordResultMap">
        SELECT * FROM meal_record 
        WHERE user_id = #{userId} 
        AND record_date = #{recordDate} 
        AND meal_type = #{mealType}
    </select>

    <!-- 根据用户ID查询最近的膳食记录 -->
    <select id="selectRecentByUserId" resultMap="MealRecordResultMap">
        SELECT * FROM meal_record 
        WHERE user_id = #{userId}
        ORDER BY record_date DESC, create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 统计用户膳食记录数量 -->
    <select id="countByUserId" parameterType="long" resultType="int">
        SELECT COUNT(*) FROM meal_record WHERE user_id = #{userId}
    </select>

    <!-- 统计用户指定日期范围内的膳食记录数量 -->
    <select id="countByUserIdAndDateRange" resultType="int">
        SELECT COUNT(*) FROM meal_record 
        WHERE user_id = #{userId} 
        AND record_date BETWEEN #{startDate} AND #{endDate}
    </select>

</mapper>

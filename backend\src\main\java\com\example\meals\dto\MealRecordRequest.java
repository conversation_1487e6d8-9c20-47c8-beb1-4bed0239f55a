package com.example.meals.dto;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 膳食记录请求DTO
 * {{ AURA-X: Add - 创建膳食记录请求DTO. Approval: 寸止. }}
 */
public class MealRecordRequest {
    
    private LocalDate recordDate;
    private String mealType; // breakfast, lunch, dinner, snack
    private String notes;
    private List<MealItemRequest> mealItems;
    
    // 膳食条目请求DTO
    public static class MealItemRequest {
        private Long foodId;
        private BigDecimal weight;
        
        public MealItemRequest() {}
        
        public MealItemRequest(Long foodId, BigDecimal weight) {
            this.foodId = foodId;
            this.weight = weight;
        }
        
        public Long getFoodId() {
            return foodId;
        }
        
        public void setFoodId(Long foodId) {
            this.foodId = foodId;
        }
        
        public BigDecimal getWeight() {
            return weight;
        }
        
        public void setWeight(BigDecimal weight) {
            this.weight = weight;
        }
        
        @Override
        public String toString() {
            return "MealItemRequest{" +
                    "foodId=" + foodId +
                    ", weight=" + weight +
                    '}';
        }
    }
    
    // 构造函数
    public MealRecordRequest() {}
    
    public MealRecordRequest(LocalDate recordDate, String mealType) {
        this.recordDate = recordDate;
        this.mealType = mealType;
    }
    
    // Getter 和 Setter 方法
    public LocalDate getRecordDate() {
        return recordDate;
    }
    
    public void setRecordDate(LocalDate recordDate) {
        this.recordDate = recordDate;
    }
    
    public String getMealType() {
        return mealType;
    }
    
    public void setMealType(String mealType) {
        this.mealType = mealType;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    public List<MealItemRequest> getMealItems() {
        return mealItems;
    }
    
    public void setMealItems(List<MealItemRequest> mealItems) {
        this.mealItems = mealItems;
    }
    
    @Override
    public String toString() {
        return "MealRecordRequest{" +
                "recordDate=" + recordDate +
                ", mealType='" + mealType + '\'' +
                ", notes='" + notes + '\'' +
                ", mealItems=" + mealItems +
                '}';
    }
}

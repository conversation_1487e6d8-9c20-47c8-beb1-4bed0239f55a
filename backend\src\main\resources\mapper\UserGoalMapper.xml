<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.meals.mapper.UserGoalMapper">

    <!-- 结果映射 -->
    <resultMap id="UserGoalResultMap" type="com.example.meals.entity.UserGoal">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="goalCode" column="goal_code"/>
        <result property="goalName" column="goal_name"/>
        <result property="goalType" column="goal_type"/>
        <result property="targetValue" column="target_value"/>
        <result property="currentValue" column="current_value"/>
        <result property="unit" column="unit"/>
        <result property="deadline" column="deadline"/>
        <result property="description" column="description"/>
        <result property="isActive" column="is_active"/>
        <result property="isCompleted" column="is_completed"/>
        <result property="progress" column="progress"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <!-- 根据用户ID查询目标列表 -->
    <select id="findByUserId" parameterType="long" resultMap="UserGoalResultMap">
        SELECT * FROM user_goals 
        WHERE user_id = #{userId} 
        ORDER BY created_at DESC
    </select>

    <!-- 根据用户ID和目标ID查询目标 -->
    <select id="findByIdAndUserId" resultMap="UserGoalResultMap">
        SELECT * FROM user_goals 
        WHERE id = #{goalId} AND user_id = #{userId}
    </select>

    <!-- 插入新的用户目标 -->
    <insert id="insert" parameterType="com.example.meals.entity.UserGoal" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_goals (
            user_id, goal_code, goal_name, goal_type, target_value, 
            current_value, unit, deadline, description, is_active, 
            is_completed, progress
        ) VALUES (
            #{userId}, #{goalCode}, #{goalName}, #{goalType}, #{targetValue}, 
            #{currentValue}, #{unit}, #{deadline}, #{description}, #{isActive}, 
            #{isCompleted}, #{progress}
        )
    </insert>

    <!-- 更新用户目标 -->
    <update id="update" parameterType="com.example.meals.entity.UserGoal">
        UPDATE user_goals SET 
            goal_name = #{goalName},
            target_value = #{targetValue},
            current_value = #{currentValue},
            deadline = #{deadline},
            description = #{description},
            is_active = #{isActive},
            is_completed = #{isCompleted},
            progress = #{progress},
            updated_at = CURRENT_TIMESTAMP
        WHERE id = #{id} AND user_id = #{userId}
    </update>

    <!-- 更新目标进度 -->
    <update id="updateProgress">
        UPDATE user_goals SET 
            current_value = #{currentValue},
            progress = #{progress},
            is_completed = #{isCompleted},
            updated_at = CURRENT_TIMESTAMP
        WHERE id = #{id} AND user_id = #{userId}
    </update>

    <!-- 删除用户目标 -->
    <delete id="deleteByIdAndUserId">
        DELETE FROM user_goals 
        WHERE id = #{goalId} AND user_id = #{userId}
    </delete>

    <!-- 统计用户活跃目标数 -->
    <select id="countActiveGoals" parameterType="long" resultType="int">
        SELECT COUNT(*) FROM user_goals 
        WHERE user_id = #{userId} AND is_active = 1
    </select>

    <!-- 统计用户已完成目标数 -->
    <select id="countCompletedGoals" parameterType="long" resultType="int">
        SELECT COUNT(*) FROM user_goals 
        WHERE user_id = #{userId} AND is_completed = 1
    </select>

    <!-- 统计用户总目标数 -->
    <select id="countTotalGoals" parameterType="long" resultType="int">
        SELECT COUNT(*) FROM user_goals 
        WHERE user_id = #{userId}
    </select>

    <!-- 计算用户目标平均进度 -->
    <select id="getAverageProgress" parameterType="long" resultType="double">
        SELECT AVG(progress) FROM user_goals 
        WHERE user_id = #{userId} AND is_active = 1
    </select>

    <!-- 检查用户是否已有相同类型的活跃目标 -->
    <select id="countActiveGoalsByType" resultType="int">
        SELECT COUNT(*) FROM user_goals 
        WHERE user_id = #{userId} AND goal_type = #{goalType} AND is_active = 1
    </select>

</mapper>

<template>
  <div v-if="show" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <!-- 模态框头部 -->
      <div class="modal-header">
        <h2 class="modal-title">
          <span class="title-icon">{{ isEditing ? '✏️' : '✨' }}</span>
          {{ isEditing ? '编辑目标' : '创建新目标' }}
        </h2>
        <button @click="$emit('close')" class="close-btn">
          <span>✕</span>
        </button>
      </div>

      <!-- 模态框内容 -->
      <div class="modal-body">
        <form @submit.prevent="handleSubmit" class="goal-form">
          <!-- 目标类型选择 -->
          <div class="form-group">
            <label class="form-label">目标类型</label>
            <div class="goal-type-grid">
              <div 
                v-for="type in goalTypes" 
                :key="type.value"
                class="goal-type-option"
                :class="{ active: formData.goalType === type.value }"
                @click="selectGoalType(type.value)"
              >
                <div class="type-icon">{{ type.icon }}</div>
                <div class="type-name">{{ type.label }}</div>
                <div class="type-desc">{{ type.description }}</div>
              </div>
            </div>
          </div>

          <!-- 目标名称 -->
          <div class="form-group">
            <label class="form-label" for="goalTitle">目标名称</label>
            <input
              id="goalTitle"
              v-model="formData.title"
              type="text"
              class="form-input"
              placeholder="为你的目标起个名字..."
              required
            />
          </div>

          <!-- 目标值设置 -->
          <div class="form-row">
            <div class="form-group">
              <label class="form-label" for="targetValue">目标值</label>
              <div class="input-with-unit">
                <input
                  id="targetValue"
                  v-model.number="formData.targetValue"
                  type="number"
                  class="form-input"
                  placeholder="0"
                  min="0"
                  step="0.1"
                  required
                />
                <span class="input-unit">{{ currentUnit }}</span>
              </div>
            </div>

            <div class="form-group">
              <label class="form-label" for="deadline">截止日期</label>
              <input
                id="deadline"
                v-model="formData.deadline"
                type="date"
                class="form-input"
                :min="today"
                required
              />
            </div>
          </div>

          <!-- 当前值（仅编辑时显示） -->
          <div v-if="isEditing" class="form-group">
            <label class="form-label" for="currentValue">当前值</label>
            <div class="input-with-unit">
              <input
                id="currentValue"
                v-model.number="formData.currentValue"
                type="number"
                class="form-input"
                placeholder="0"
                min="0"
                step="0.1"
              />
              <span class="input-unit">{{ currentUnit }}</span>
            </div>
          </div>

          <!-- 目标描述 -->
          <div class="form-group">
            <label class="form-label" for="description">目标描述（可选）</label>
            <textarea
              id="description"
              v-model="formData.description"
              class="form-textarea"
              placeholder="描述一下你的目标，让它更有意义..."
              rows="3"
            ></textarea>
          </div>

          <!-- 预设目标建议 -->
          <div v-if="!isEditing && selectedTypePresets.length > 0" class="form-group">
            <label class="form-label">快速设置</label>
            <div class="preset-options">
              <button
                v-for="preset in selectedTypePresets"
                :key="preset.value"
                type="button"
                class="preset-btn"
                @click="applyPreset(preset)"
              >
                {{ preset.label }}
              </button>
            </div>
          </div>
        </form>
      </div>

      <!-- 模态框底部 -->
      <div class="modal-footer">
        <button @click="$emit('close')" type="button" class="btn btn-secondary">
          取消
        </button>
        <button 
          @click="handleSubmit" 
          type="button" 
          class="btn btn-primary"
          :disabled="!isFormValid || isSubmitting"
        >
          <span v-if="isSubmitting" class="loading-spinner"></span>
          {{ isEditing ? '保存更改' : '创建目标' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { getDefaultUnit } from '../utils/healthGoalApi'
import type { UserGoal } from '../utils/healthGoalApi'

// Props
interface Props {
  show: boolean
  editingGoal?: UserGoal | null
}

const props = withDefaults(defineProps<Props>(), {
  editingGoal: null
})

// Emits
const emit = defineEmits<{
  close: []
  submit: [goalData: any]
}>()

// 响应式数据
const isSubmitting = ref(false)
const today = new Date().toISOString().split('T')[0]

const formData = reactive({
  goalType: '',
  title: '',
  targetValue: 0,
  currentValue: 0,
  deadline: '',
  description: ''
})

// 计算属性
const isEditing = computed(() => !!props.editingGoal)

const currentUnit = computed(() => {
  return getDefaultUnit(formData.goalType)
})

const isFormValid = computed(() => {
  return formData.goalType && 
         formData.title.trim() && 
         formData.targetValue > 0 && 
         formData.deadline
})

// 目标类型定义
const goalTypes = [
  {
    value: 'calories',
    label: '卡路里',
    icon: '🔥',
    description: '控制每日卡路里摄入'
  },
  {
    value: 'weight',
    label: '体重',
    icon: '⚖️',
    description: '达到理想体重'
  },
  {
    value: 'exercise',
    label: '运动',
    icon: '💪',
    description: '保持运动习惯'
  },
  {
    value: 'nutrition',
    label: '营养',
    icon: '🥗',
    description: '均衡营养摄入'
  },
  {
    value: 'water',
    label: '饮水',
    icon: '💧',
    description: '保持充足水分'
  },
  {
    value: 'sleep',
    label: '睡眠',
    icon: '😴',
    description: '改善睡眠质量'
  }
]

// 预设目标
const presets = {
  calories: [
    { label: '减重 (1500 kcal/天)', value: 1500 },
    { label: '维持 (2000 kcal/天)', value: 2000 },
    { label: '增重 (2500 kcal/天)', value: 2500 }
  ],
  weight: [
    { label: '减重 5kg', value: 5 },
    { label: '减重 10kg', value: 10 },
    { label: '增重 5kg', value: 5 }
  ],
  exercise: [
    { label: '每天 30 分钟', value: 30 },
    { label: '每天 60 分钟', value: 60 },
    { label: '每周 150 分钟', value: 150 }
  ],
  water: [
    { label: '每天 8 杯 (2000ml)', value: 2000 },
    { label: '每天 10 杯 (2500ml)', value: 2500 },
    { label: '每天 12 杯 (3000ml)', value: 3000 }
  ],
  sleep: [
    { label: '每天 7 小时', value: 7 },
    { label: '每天 8 小时', value: 8 },
    { label: '每天 9 小时', value: 9 }
  ],
  nutrition: [
    { label: '蛋白质 100g/天', value: 100 },
    { label: '纤维 25g/天', value: 25 },
    { label: '维生素C 100mg/天', value: 100 }
  ]
}

const selectedTypePresets = computed(() => {
  return presets[formData.goalType as keyof typeof presets] || []
})

// 方法
const selectGoalType = (type: string) => {
  formData.goalType = type
  if (!formData.title) {
    const typeInfo = goalTypes.find(t => t.value === type)
    formData.title = `我的${typeInfo?.label}目标`
  }
}

const applyPreset = (preset: { label: string; value: number }) => {
  formData.targetValue = preset.value
}

const handleOverlayClick = () => {
  emit('close')
}

const handleSubmit = async () => {
  if (!isFormValid.value || isSubmitting.value) return

  isSubmitting.value = true
  try {
    const goalData = {
      ...formData,
      unit: currentUnit.value
    }
    emit('submit', goalData)
  } finally {
    isSubmitting.value = false
  }
}

// 监听编辑目标变化
watch(() => props.editingGoal, (goal) => {
  if (goal) {
    formData.goalType = goal.goalType
    formData.title = goal.goalName
    formData.targetValue = goal.targetValue
    formData.currentValue = goal.currentValue
    formData.deadline = goal.deadline
    formData.description = goal.description || ''
  } else {
    // 重置表单
    Object.assign(formData, {
      goalType: '',
      title: '',
      targetValue: 0,
      currentValue: 0,
      deadline: '',
      description: ''
    })
  }
}, { immediate: true })

// 监听显示状态
watch(() => props.show, (show) => {
  if (show && !props.editingGoal) {
    // 设置默认截止日期为一个月后
    const nextMonth = new Date()
    nextMonth.setMonth(nextMonth.getMonth() + 1)
    formData.deadline = nextMonth.toISOString().split('T')[0]
  }
})
</script>

<style scoped>
/* 模态框遮罩 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
  animation: fadeIn 0.3s ease-out;
}

/* 模态框容器 */
.modal-container {
  background: white;
  border-radius: 24px;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.2);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  animation: slideInScale 0.4s ease-out;
  position: relative;
}

.modal-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

/* 模态框头部 */
.modal-header {
  padding: 2rem 2rem 1rem 2rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.title-icon {
  font-size: 2rem;
}

.close-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.05);
  color: #718096;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(0, 0, 0, 0.1);
  transform: scale(1.1);
}

/* 模态框内容 */
.modal-body {
  padding: 1rem 2rem 2rem 2rem;
  max-height: 60vh;
  overflow-y: auto;
}

/* 表单样式 */
.goal-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-label {
  font-size: 0.95rem;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 0.5rem;
}

.form-input {
  padding: 0.875rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-textarea {
  padding: 0.875rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.form-textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-with-unit {
  position: relative;
  display: flex;
  align-items: center;
}

.input-with-unit .form-input {
  padding-right: 4rem;
  flex: 1;
}

.input-unit {
  position: absolute;
  right: 1rem;
  color: #718096;
  font-weight: 500;
  font-size: 0.9rem;
  pointer-events: none;
}

/* 目标类型网格 */
.goal-type-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 1rem;
}

.goal-type-option {
  padding: 1.5rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  position: relative;
  overflow: hidden;
}

.goal-type-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.goal-type-option:hover::before {
  opacity: 0.05;
}

.goal-type-option.active {
  border-color: #667eea;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
}

.goal-type-option.active::before {
  opacity: 0.1;
}

.type-icon {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  position: relative;
  z-index: 1;
}

.type-name {
  font-size: 1rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.3rem;
  position: relative;
  z-index: 1;
}

.type-desc {
  font-size: 0.8rem;
  color: #718096;
  line-height: 1.3;
  position: relative;
  z-index: 1;
}

/* 预设选项 */
.preset-options {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
}

.preset-btn {
  padding: 0.6rem 1.2rem;
  border: 2px solid #e2e8f0;
  border-radius: 20px;
  background: white;
  color: #4a5568;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.preset-btn:hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
  color: #667eea;
  transform: translateY(-1px);
}

/* 模态框底部 */
.modal-footer {
  padding: 1.5rem 2rem 2rem 2rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.btn {
  padding: 0.875rem 2rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 120px;
  justify-content: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-secondary {
  background: #f7fafc;
  color: #4a5568;
  border: 2px solid #e2e8f0;
}

.btn-secondary:hover:not(:disabled) {
  background: #edf2f7;
  border-color: #cbd5e0;
  transform: translateY(-1px);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* 加载动画 */
.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 1rem;
  }

  .modal-container {
    max-height: 95vh;
  }

  .modal-header {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
  }

  .modal-title {
    font-size: 1.5rem;
  }

  .modal-body {
    padding: 1rem 1.5rem 1.5rem 1.5rem;
  }

  .modal-footer {
    padding: 1rem 1.5rem 1.5rem 1.5rem;
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .goal-type-grid {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 0.8rem;
  }

  .goal-type-option {
    padding: 1.2rem 0.8rem;
  }

  .type-icon {
    font-size: 2rem;
  }

  .preset-options {
    flex-direction: column;
  }

  .preset-btn {
    width: 100%;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .modal-title {
    font-size: 1.3rem;
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }

  .goal-type-grid {
    grid-template-columns: 1fr;
  }

  .goal-type-option {
    padding: 1rem;
  }
}

/* 滚动条样式 */
.modal-body::-webkit-scrollbar {
  width: 6px;
}

.modal-body::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}
</style>

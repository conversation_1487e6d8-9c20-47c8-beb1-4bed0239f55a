package com.example.meals.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 膳食条目实体类
 * {{ AURA-X: Add - 创建膳食条目实体类. Approval: 寸止. }}
 */
public class MealItem {
    
    private Long id;
    private Long mealRecordId;
    private Long foodId;
    private String foodName;
    private String foodCode;
    private BigDecimal weight;
    private BigDecimal energy;
    private BigDecimal protein;
    private BigDecimal fat;
    private BigDecimal carbohydrate;
    private BigDecimal dietaryFiber;
    private LocalDateTime createTime;
    
    // 构造函数
    public MealItem() {}
    
    public MealItem(Long mealRecordId, Long foodId, String foodName, String foodCode, BigDecimal weight) {
        this.mealRecordId = mealRecordId;
        this.foodId = foodId;
        this.foodName = foodName;
        this.foodCode = foodCode;
        this.weight = weight;
    }
    
    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getMealRecordId() {
        return mealRecordId;
    }
    
    public void setMealRecordId(Long mealRecordId) {
        this.mealRecordId = mealRecordId;
    }
    
    public Long getFoodId() {
        return foodId;
    }
    
    public void setFoodId(Long foodId) {
        this.foodId = foodId;
    }
    
    public String getFoodName() {
        return foodName;
    }
    
    public void setFoodName(String foodName) {
        this.foodName = foodName;
    }
    
    public String getFoodCode() {
        return foodCode;
    }
    
    public void setFoodCode(String foodCode) {
        this.foodCode = foodCode;
    }
    
    public BigDecimal getWeight() {
        return weight;
    }
    
    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }
    
    public BigDecimal getEnergy() {
        return energy;
    }
    
    public void setEnergy(BigDecimal energy) {
        this.energy = energy;
    }
    
    public BigDecimal getProtein() {
        return protein;
    }
    
    public void setProtein(BigDecimal protein) {
        this.protein = protein;
    }
    
    public BigDecimal getFat() {
        return fat;
    }
    
    public void setFat(BigDecimal fat) {
        this.fat = fat;
    }
    
    public BigDecimal getCarbohydrate() {
        return carbohydrate;
    }
    
    public void setCarbohydrate(BigDecimal carbohydrate) {
        this.carbohydrate = carbohydrate;
    }
    
    public BigDecimal getDietaryFiber() {
        return dietaryFiber;
    }
    
    public void setDietaryFiber(BigDecimal dietaryFiber) {
        this.dietaryFiber = dietaryFiber;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    @Override
    public String toString() {
        return "MealItem{" +
                "id=" + id +
                ", mealRecordId=" + mealRecordId +
                ", foodId=" + foodId +
                ", foodName='" + foodName + '\'' +
                ", foodCode='" + foodCode + '\'' +
                ", weight=" + weight +
                ", energy=" + energy +
                ", protein=" + protein +
                ", fat=" + fat +
                ", carbohydrate=" + carbohydrate +
                ", dietaryFiber=" + dietaryFiber +
                ", createTime=" + createTime +
                '}';
    }
}

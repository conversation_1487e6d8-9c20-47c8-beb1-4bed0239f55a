<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.meals.mapper.ChinaFoodCategoryMapper">

    <!-- 结果映射 -->
    <resultMap id="ChinaFoodCategoryResultMap" type="com.example.meals.entity.ChinaFoodCategory">
        <id column="id" property="id" />
        <result column="category_code" property="categoryCode" />
        <result column="category_name" property="categoryName" />
        <result column="parent_id" property="parentId" />
        <result column="sort_order" property="sortOrder" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 获取所有分类 -->
    <select id="getAllCategories" resultMap="ChinaFoodCategoryResultMap">
        SELECT * FROM china_food_categories 
        ORDER BY sort_order
    </select>

    <!-- 根据分类代码获取分类名称 -->
    <select id="getCategoryNameByCode" resultType="string">
        SELECT category_name FROM china_food_categories 
        WHERE category_code = #{categoryCode}
    </select>

</mapper>

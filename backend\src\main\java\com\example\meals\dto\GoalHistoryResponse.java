package com.example.meals.dto;

import com.example.meals.entity.UserGoalHistory;

/**
 * 目标历史记录响应DTO
 */
public class GoalHistoryResponse {
    
    private Long id;
    private Long goalId;
    private String date;
    private Double value;
    private Double progress;
    private String notes;
    
    // 构造函数
    public GoalHistoryResponse() {}
    
    public GoalHistoryResponse(UserGoalHistory history) {
        this.id = history.getId();
        this.goalId = history.getGoalId();
        this.date = history.getRecordDate().toString();
        this.value = history.getValue();
        this.progress = history.getProgress();
        this.notes = history.getNotes();
    }
    
    public GoalHistoryResponse(String date, Double value, Double progress) {
        this.date = date;
        this.value = value;
        this.progress = progress;
    }
    
    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getGoalId() {
        return goalId;
    }
    
    public void setGoalId(Long goalId) {
        this.goalId = goalId;
    }
    
    public String getDate() {
        return date;
    }
    
    public void setDate(String date) {
        this.date = date;
    }
    
    public Double getValue() {
        return value;
    }
    
    public void setValue(Double value) {
        this.value = value;
    }
    
    public Double getProgress() {
        return progress;
    }
    
    public void setProgress(Double progress) {
        this.progress = progress;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    @Override
    public String toString() {
        return "GoalHistoryResponse{" +
                "id=" + id +
                ", goalId=" + goalId +
                ", date='" + date + '\'' +
                ", value=" + value +
                ", progress=" + progress +
                ", notes='" + notes + '\'' +
                '}';
    }
}

package com.example.meals.entity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 膳食记录实体类
 * {{ AURA-X: Add - 创建膳食记录实体类. Approval: 寸止. }}
 */
public class MealRecord {
    
    private Long id;
    private Long userId;
    private LocalDate recordDate;
    private String mealType; // breakfast, lunch, dinner, snack
    private BigDecimal totalEnergy;
    private BigDecimal totalProtein;
    private BigDecimal totalFat;
    private BigDecimal totalCarbohydrate;
    private BigDecimal totalDietaryFiber;
    private String notes;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    
    // 关联的膳食条目列表（非数据库字段）
    private List<MealItem> mealItems;
    
    // 构造函数
    public MealRecord() {}
    
    public MealRecord(Long userId, LocalDate recordDate, String mealType) {
        this.userId = userId;
        this.recordDate = recordDate;
        this.mealType = mealType;
        this.totalEnergy = BigDecimal.ZERO;
        this.totalProtein = BigDecimal.ZERO;
        this.totalFat = BigDecimal.ZERO;
        this.totalCarbohydrate = BigDecimal.ZERO;
        this.totalDietaryFiber = BigDecimal.ZERO;
    }
    
    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public LocalDate getRecordDate() {
        return recordDate;
    }
    
    public void setRecordDate(LocalDate recordDate) {
        this.recordDate = recordDate;
    }
    
    public String getMealType() {
        return mealType;
    }
    
    public void setMealType(String mealType) {
        this.mealType = mealType;
    }
    
    public BigDecimal getTotalEnergy() {
        return totalEnergy;
    }
    
    public void setTotalEnergy(BigDecimal totalEnergy) {
        this.totalEnergy = totalEnergy;
    }
    
    public BigDecimal getTotalProtein() {
        return totalProtein;
    }
    
    public void setTotalProtein(BigDecimal totalProtein) {
        this.totalProtein = totalProtein;
    }
    
    public BigDecimal getTotalFat() {
        return totalFat;
    }
    
    public void setTotalFat(BigDecimal totalFat) {
        this.totalFat = totalFat;
    }
    
    public BigDecimal getTotalCarbohydrate() {
        return totalCarbohydrate;
    }
    
    public void setTotalCarbohydrate(BigDecimal totalCarbohydrate) {
        this.totalCarbohydrate = totalCarbohydrate;
    }
    
    public BigDecimal getTotalDietaryFiber() {
        return totalDietaryFiber;
    }
    
    public void setTotalDietaryFiber(BigDecimal totalDietaryFiber) {
        this.totalDietaryFiber = totalDietaryFiber;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    public List<MealItem> getMealItems() {
        return mealItems;
    }
    
    public void setMealItems(List<MealItem> mealItems) {
        this.mealItems = mealItems;
    }
    
    /**
     * 计算并更新营养总值
     */
    public void calculateTotalNutrition() {
        if (mealItems == null || mealItems.isEmpty()) {
            this.totalEnergy = BigDecimal.ZERO;
            this.totalProtein = BigDecimal.ZERO;
            this.totalFat = BigDecimal.ZERO;
            this.totalCarbohydrate = BigDecimal.ZERO;
            this.totalDietaryFiber = BigDecimal.ZERO;
            return;
        }
        
        this.totalEnergy = mealItems.stream()
                .map(MealItem::getEnergy)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        this.totalProtein = mealItems.stream()
                .map(MealItem::getProtein)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        this.totalFat = mealItems.stream()
                .map(MealItem::getFat)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        this.totalCarbohydrate = mealItems.stream()
                .map(MealItem::getCarbohydrate)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        this.totalDietaryFiber = mealItems.stream()
                .map(MealItem::getDietaryFiber)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    @Override
    public String toString() {
        return "MealRecord{" +
                "id=" + id +
                ", userId=" + userId +
                ", recordDate=" + recordDate +
                ", mealType='" + mealType + '\'' +
                ", totalEnergy=" + totalEnergy +
                ", totalProtein=" + totalProtein +
                ", totalFat=" + totalFat +
                ", totalCarbohydrate=" + totalCarbohydrate +
                ", totalDietaryFiber=" + totalDietaryFiber +
                ", notes='" + notes + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}

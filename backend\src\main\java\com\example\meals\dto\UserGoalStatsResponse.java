package com.example.meals.dto;

/**
 * 用户健康目标统计响应DTO
 */
public class UserGoalStatsResponse {
    
    private Integer activeGoals;        // 活跃目标数
    private Integer completedGoals;     // 已完成目标数
    private Integer totalGoals;         // 总目标数
    private Double overallProgress;     // 总体进度
    private Integer currentStreak;      // 当前连续天数
    
    // 构造函数
    public UserGoalStatsResponse() {}
    
    public UserGoalStatsResponse(Integer activeGoals, Integer completedGoals, Integer totalGoals, 
                                Double overallProgress, Integer currentStreak) {
        this.activeGoals = activeGoals;
        this.completedGoals = completedGoals;
        this.totalGoals = totalGoals;
        this.overallProgress = overallProgress;
        this.currentStreak = currentStreak;
    }
    
    // Getter 和 Setter 方法
    public Integer getActiveGoals() {
        return activeGoals;
    }
    
    public void setActiveGoals(Integer activeGoals) {
        this.activeGoals = activeGoals;
    }
    
    public Integer getCompletedGoals() {
        return completedGoals;
    }
    
    public void setCompletedGoals(Integer completedGoals) {
        this.completedGoals = completedGoals;
    }
    
    public Integer getTotalGoals() {
        return totalGoals;
    }
    
    public void setTotalGoals(Integer totalGoals) {
        this.totalGoals = totalGoals;
    }
    
    public Double getOverallProgress() {
        return overallProgress;
    }
    
    public void setOverallProgress(Double overallProgress) {
        this.overallProgress = overallProgress;
    }
    
    public Integer getCurrentStreak() {
        return currentStreak;
    }
    
    public void setCurrentStreak(Integer currentStreak) {
        this.currentStreak = currentStreak;
    }
    
    @Override
    public String toString() {
        return "UserGoalStatsResponse{" +
                "activeGoals=" + activeGoals +
                ", completedGoals=" + completedGoals +
                ", totalGoals=" + totalGoals +
                ", overallProgress=" + overallProgress +
                ", currentStreak=" + currentStreak +
                '}';
    }
}

package com.example.meals.dto;

import com.example.meals.entity.ChinaFood;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 营养分析响应DTO
 */
public class NutritionResponse {
    
    private FoodInfo foodInfo;
    private CalculatedNutrition calculatedNutrition;
    private DetailedNutrients detailedNutrients;
    
    public static class FoodInfo {
        private Long id;
        private String name;
        private String code;
        private String category;
        private Integer weight;
        
        public FoodInfo(ChinaFood food, Integer weight) {
            this.id = food.getId();
            this.name = food.getFoodName();
            this.code = food.getFoodCode();
            this.weight = weight;
            this.category = "食物";
        }
        
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }
        public Integer getWeight() { return weight; }
        public void setWeight(Integer weight) { this.weight = weight; }
    }
    
    public static class CalculatedNutrition {
        private Integer calories;
        private BigDecimal protein;
        private BigDecimal fat;
        private BigDecimal carbohydrates;
        private BigDecimal fiber;
        private BigDecimal sugar;
        private BigDecimal sodium;
        private BigDecimal cholesterol;
        
        public CalculatedNutrition(ChinaFood food, Integer weight) {
            BigDecimal ratio = new BigDecimal(weight).divide(new BigDecimal(100), 4, RoundingMode.HALF_UP);
            
            this.calories = food.getEnergyKcal() != null ? 
                new BigDecimal(food.getEnergyKcal()).multiply(ratio).setScale(0, RoundingMode.HALF_UP).intValue() : 0;
            
            this.protein = calculateNutrient(food.getProtein(), ratio);
            this.fat = calculateNutrient(food.getFat(), ratio);
            this.carbohydrates = calculateNutrient(food.getCho(), ratio);
            this.fiber = calculateNutrient(food.getDietaryFiber(), ratio);
            this.sodium = calculateNutrient(food.getNa(), ratio);
            this.cholesterol = calculateNutrient(food.getCholesterol(), ratio);
            this.sugar = null;
        }
        
        private BigDecimal calculateNutrient(BigDecimal value, BigDecimal ratio) {
            if (value == null) return BigDecimal.ZERO;
            return value.multiply(ratio).setScale(2, RoundingMode.HALF_UP);
        }
        
        public Integer getCalories() { return calories; }
        public void setCalories(Integer calories) { this.calories = calories; }
        public BigDecimal getProtein() { return protein; }
        public void setProtein(BigDecimal protein) { this.protein = protein; }
        public BigDecimal getFat() { return fat; }
        public void setFat(BigDecimal fat) { this.fat = fat; }
        public BigDecimal getCarbohydrates() { return carbohydrates; }
        public void setCarbohydrates(BigDecimal carbohydrates) { this.carbohydrates = carbohydrates; }
        public BigDecimal getFiber() { return fiber; }
        public void setFiber(BigDecimal fiber) { this.fiber = fiber; }
        public BigDecimal getSugar() { return sugar; }
        public void setSugar(BigDecimal sugar) { this.sugar = sugar; }
        public BigDecimal getSodium() { return sodium; }
        public void setSodium(BigDecimal sodium) { this.sodium = sodium; }
        public BigDecimal getCholesterol() { return cholesterol; }
        public void setCholesterol(BigDecimal cholesterol) { this.cholesterol = cholesterol; }
    }
    
    public static class DetailedNutrients {
        private BigDecimal vitaminA;
        private BigDecimal vitaminC;
        private BigDecimal vitaminE;
        private BigDecimal thiamin;
        private BigDecimal riboflavin;
        private BigDecimal niacin;
        private BigDecimal calcium;
        private BigDecimal iron;
        private BigDecimal zinc;
        private BigDecimal selenium;
        private BigDecimal phosphorus;
        private BigDecimal potassium;
        private BigDecimal magnesium;
        private BigDecimal copper;
        private BigDecimal manganese;
        private BigDecimal water;
        private BigDecimal ash;
        
        public DetailedNutrients(ChinaFood food, Integer weight) {
            BigDecimal ratio = new BigDecimal(weight).divide(new BigDecimal(100), 4, RoundingMode.HALF_UP);
            
            this.vitaminA = calculateNutrient(food.getVitaminA(), ratio);
            this.vitaminC = calculateNutrient(food.getVitaminC(), ratio);
            this.vitaminE = calculateNutrient(food.getVitaminETotal(), ratio);
            this.thiamin = calculateNutrient(food.getThiamin(), ratio);
            this.riboflavin = calculateNutrient(food.getRiboflavin(), ratio);
            this.niacin = calculateNutrient(food.getNiacin(), ratio);
            this.calcium = calculateNutrient(food.getCa(), ratio);
            this.iron = calculateNutrient(food.getFe(), ratio);
            this.zinc = calculateNutrient(food.getZn(), ratio);
            this.selenium = calculateNutrient(food.getSe(), ratio);
            this.phosphorus = calculateNutrient(food.getP(), ratio);
            this.potassium = calculateNutrient(food.getK(), ratio);
            this.magnesium = calculateNutrient(food.getMg(), ratio);
            this.copper = calculateNutrient(food.getCu(), ratio);
            this.manganese = calculateNutrient(food.getMn(), ratio);
            this.water = calculateNutrient(food.getWater(), ratio);
            this.ash = calculateNutrient(food.getAsh(), ratio);
        }
        
        private BigDecimal calculateNutrient(BigDecimal value, BigDecimal ratio) {
            if (value == null) return BigDecimal.ZERO;
            return value.multiply(ratio).setScale(2, RoundingMode.HALF_UP);
        }
        
        public BigDecimal getVitaminA() { return vitaminA; }
        public void setVitaminA(BigDecimal vitaminA) { this.vitaminA = vitaminA; }
        public BigDecimal getVitaminC() { return vitaminC; }
        public void setVitaminC(BigDecimal vitaminC) { this.vitaminC = vitaminC; }
        public BigDecimal getVitaminE() { return vitaminE; }
        public void setVitaminE(BigDecimal vitaminE) { this.vitaminE = vitaminE; }
        public BigDecimal getThiamin() { return thiamin; }
        public void setThiamin(BigDecimal thiamin) { this.thiamin = thiamin; }
        public BigDecimal getRiboflavin() { return riboflavin; }
        public void setRiboflavin(BigDecimal riboflavin) { this.riboflavin = riboflavin; }
        public BigDecimal getNiacin() { return niacin; }
        public void setNiacin(BigDecimal niacin) { this.niacin = niacin; }
        public BigDecimal getCalcium() { return calcium; }
        public void setCalcium(BigDecimal calcium) { this.calcium = calcium; }
        public BigDecimal getIron() { return iron; }
        public void setIron(BigDecimal iron) { this.iron = iron; }
        public BigDecimal getZinc() { return zinc; }
        public void setZinc(BigDecimal zinc) { this.zinc = zinc; }
        public BigDecimal getSelenium() { return selenium; }
        public void setSelenium(BigDecimal selenium) { this.selenium = selenium; }
        public BigDecimal getPhosphorus() { return phosphorus; }
        public void setPhosphorus(BigDecimal phosphorus) { this.phosphorus = phosphorus; }
        public BigDecimal getPotassium() { return potassium; }
        public void setPotassium(BigDecimal potassium) { this.potassium = potassium; }
        public BigDecimal getMagnesium() { return magnesium; }
        public void setMagnesium(BigDecimal magnesium) { this.magnesium = magnesium; }
        public BigDecimal getCopper() { return copper; }
        public void setCopper(BigDecimal copper) { this.copper = copper; }
        public BigDecimal getManganese() { return manganese; }
        public void setManganese(BigDecimal manganese) { this.manganese = manganese; }
        public BigDecimal getWater() { return water; }
        public void setWater(BigDecimal water) { this.water = water; }
        public BigDecimal getAsh() { return ash; }
        public void setAsh(BigDecimal ash) { this.ash = ash; }
    }
    
    public NutritionResponse(ChinaFood food, Integer weight) {
        this.foodInfo = new FoodInfo(food, weight);
        this.calculatedNutrition = new CalculatedNutrition(food, weight);
        this.detailedNutrients = new DetailedNutrients(food, weight);
    }
    
    public FoodInfo getFoodInfo() { return foodInfo; }
    public void setFoodInfo(FoodInfo foodInfo) { this.foodInfo = foodInfo; }
    public CalculatedNutrition getCalculatedNutrition() { return calculatedNutrition; }
    public void setCalculatedNutrition(CalculatedNutrition calculatedNutrition) { this.calculatedNutrition = calculatedNutrition; }
    public DetailedNutrients getDetailedNutrients() { return detailedNutrients; }
    public void setDetailedNutrients(DetailedNutrients detailedNutrients) { this.detailedNutrients = detailedNutrients; }
}

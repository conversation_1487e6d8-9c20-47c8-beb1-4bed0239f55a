package com.example.meals.mapper;

import com.example.meals.entity.ChinaFoodCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 中国食物分类Mapper接口
 */
@Mapper
public interface ChinaFoodCategoryMapper {
    
    /**
     * 获取所有分类
     * @return 分类列表
     */
    List<ChinaFoodCategory> getAllCategories();
    
    /**
     * 根据分类代码获取分类名称
     * @param categoryCode 分类代码
     * @return 分类名称
     */
    String getCategoryNameByCode(@Param("categoryCode") String categoryCode);
}

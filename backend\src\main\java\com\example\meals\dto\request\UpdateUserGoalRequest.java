package com.example.meals.dto.request;

/**
 * 更新用户健康目标请求DTO
 */
public class UpdateUserGoalRequest {
    
    private Double targetValue;
    private Double currentValue;
    private String deadline;
    private String description;
    private Boolean isActive;
    
    // 构造函数
    public UpdateUserGoalRequest() {}
    
    // Getter 和 Setter 方法
    public Double getTargetValue() {
        return targetValue;
    }
    
    public void setTargetValue(Double targetValue) {
        this.targetValue = targetValue;
    }
    
    public Double getCurrentValue() {
        return currentValue;
    }
    
    public void setCurrentValue(Double currentValue) {
        this.currentValue = currentValue;
    }
    
    public String getDeadline() {
        return deadline;
    }
    
    public void setDeadline(String deadline) {
        this.deadline = deadline;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    @Override
    public String toString() {
        return "UpdateUserGoalRequest{" +
                "targetValue=" + targetValue +
                ", currentValue=" + currentValue +
                ", deadline='" + deadline + '\'' +
                ", description='" + description + '\'' +
                ", isActive=" + isActive +
                '}';
    }
}

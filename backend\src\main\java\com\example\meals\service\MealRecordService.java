package com.example.meals.service;

import com.example.meals.dto.MealRecordRequest;
import com.example.meals.dto.MealRecordResponse;
import com.example.meals.common.Result;

import java.time.LocalDate;
import java.util.List;

/**
 * 膳食记录服务接口
 * {{ AURA-X: Add - 创建膳食记录服务接口. Approval: 寸止. }}
 */
public interface MealRecordService {
    
    /**
     * 创建膳食记录
     * @param userId 用户ID
     * @param request 膳食记录请求
     * @return 膳食记录响应
     */
    Result<MealRecordResponse> createMealRecord(Long userId, MealRecordRequest request);
    
    /**
     * 更新膳食记录
     * @param userId 用户ID
     * @param recordId 膳食记录ID
     * @param request 膳食记录请求
     * @return 膳食记录响应
     */
    Result<MealRecordResponse> updateMealRecord(Long userId, Long recordId, MealRecordRequest request);
    
    /**
     * 删除膳食记录
     * @param userId 用户ID
     * @param recordId 膳食记录ID
     * @return 删除结果
     */
    Result<Void> deleteMealRecord(Long userId, Long recordId);
    
    /**
     * 根据ID获取膳食记录详情
     * @param userId 用户ID
     * @param recordId 膳食记录ID
     * @return 膳食记录响应
     */
    Result<MealRecordResponse> getMealRecordById(Long userId, Long recordId);
    
    /**
     * 获取用户指定日期的膳食记录
     * @param userId 用户ID
     * @param recordDate 记录日期
     * @return 膳食记录列表
     */
    Result<List<MealRecordResponse>> getMealRecordsByDate(Long userId, LocalDate recordDate);
    
    /**
     * 获取用户指定日期范围的膳食记录
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 膳食记录列表
     */
    Result<List<MealRecordResponse>> getMealRecordsByDateRange(Long userId, LocalDate startDate, LocalDate endDate);
    
    /**
     * 获取用户最近的膳食记录
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 膳食记录列表
     */
    Result<List<MealRecordResponse>> getRecentMealRecords(Long userId, Integer limit);
    
    /**
     * 向膳食记录添加食物条目
     * @param userId 用户ID
     * @param recordId 膳食记录ID
     * @param foodId 食物ID
     * @param weight 重量
     * @return 更新后的膳食记录
     */
    Result<MealRecordResponse> addFoodToMealRecord(Long userId, Long recordId, Long foodId, java.math.BigDecimal weight);
    
    /**
     * 从膳食记录移除食物条目
     * @param userId 用户ID
     * @param recordId 膳食记录ID
     * @param itemId 膳食条目ID
     * @return 更新后的膳食记录
     */
    Result<MealRecordResponse> removeFoodFromMealRecord(Long userId, Long recordId, Long itemId);
    
    /**
     * 更新膳食条目的重量
     * @param userId 用户ID
     * @param recordId 膳食记录ID
     * @param itemId 膳食条目ID
     * @param weight 新重量
     * @return 更新后的膳食记录
     */
    Result<MealRecordResponse> updateMealItemWeight(Long userId, Long recordId, Long itemId, java.math.BigDecimal weight);
    
    /**
     * 获取用户膳食记录统计信息
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计信息
     */
    Result<MealRecordStatistics> getMealRecordStatistics(Long userId, LocalDate startDate, LocalDate endDate);
    
    /**
     * 膳食记录统计信息DTO
     */
    class MealRecordStatistics {
        private int totalRecords;
        private int totalDays;
        private java.math.BigDecimal avgDailyEnergy;
        private java.math.BigDecimal avgDailyProtein;
        private java.math.BigDecimal avgDailyFat;
        private java.math.BigDecimal avgDailyCarbohydrate;
        private java.math.BigDecimal avgDailyDietaryFiber;
        
        // 构造函数
        public MealRecordStatistics() {}
        
        // Getter 和 Setter 方法
        public int getTotalRecords() { return totalRecords; }
        public void setTotalRecords(int totalRecords) { this.totalRecords = totalRecords; }
        
        public int getTotalDays() { return totalDays; }
        public void setTotalDays(int totalDays) { this.totalDays = totalDays; }
        
        public java.math.BigDecimal getAvgDailyEnergy() { return avgDailyEnergy; }
        public void setAvgDailyEnergy(java.math.BigDecimal avgDailyEnergy) { this.avgDailyEnergy = avgDailyEnergy; }
        
        public java.math.BigDecimal getAvgDailyProtein() { return avgDailyProtein; }
        public void setAvgDailyProtein(java.math.BigDecimal avgDailyProtein) { this.avgDailyProtein = avgDailyProtein; }
        
        public java.math.BigDecimal getAvgDailyFat() { return avgDailyFat; }
        public void setAvgDailyFat(java.math.BigDecimal avgDailyFat) { this.avgDailyFat = avgDailyFat; }
        
        public java.math.BigDecimal getAvgDailyCarbohydrate() { return avgDailyCarbohydrate; }
        public void setAvgDailyCarbohydrate(java.math.BigDecimal avgDailyCarbohydrate) { this.avgDailyCarbohydrate = avgDailyCarbohydrate; }
        
        public java.math.BigDecimal getAvgDailyDietaryFiber() { return avgDailyDietaryFiber; }
        public void setAvgDailyDietaryFiber(java.math.BigDecimal avgDailyDietaryFiber) { this.avgDailyDietaryFiber = avgDailyDietaryFiber; }
    }
}

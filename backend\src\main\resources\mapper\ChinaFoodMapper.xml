<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.meals.mapper.ChinaFoodMapper">

    <!-- 结果映射 -->
    <resultMap id="ChinaFoodResultMap" type="com.example.meals.entity.ChinaFood">
        <id column="id" property="id" />
        <result column="food_code" property="foodCode" />
        <result column="food_name" property="foodName" />
        <result column="edible" property="edible" />
        <result column="water" property="water" />
        <result column="energy_kcal" property="energyKcal" />
        <result column="energy_kj" property="energyKj" />
        <result column="protein" property="protein" />
        <result column="fat" property="fat" />
        <result column="cho" property="cho" />
        <result column="dietary_fiber" property="dietaryFiber" />
        <result column="cholesterol" property="cholesterol" />
        <result column="ash" property="ash" />
        <result column="vitamin_a" property="vitaminA" />
        <result column="carotene" property="carotene" />
        <result column="retinol" property="retinol" />
        <result column="thiamin" property="thiamin" />
        <result column="riboflavin" property="riboflavin" />
        <result column="niacin" property="niacin" />
        <result column="vitamin_c" property="vitaminC" />
        <result column="vitamin_e_total" property="vitaminETotal" />
        <result column="vitamin_e1" property="vitaminE1" />
        <result column="vitamin_e2" property="vitaminE2" />
        <result column="vitamin_e3" property="vitaminE3" />
        <result column="ca" property="ca" />
        <result column="p" property="p" />
        <result column="k" property="k" />
        <result column="na" property="na" />
        <result column="mg" property="mg" />
        <result column="fe" property="fe" />
        <result column="zn" property="zn" />
        <result column="se" property="se" />
        <result column="cu" property="cu" />
        <result column="mn" property="mn" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 根据食物名称搜索食物（模糊匹配） -->
    <select id="searchByName" resultMap="ChinaFoodResultMap">
        SELECT * FROM china_foods 
        WHERE food_name LIKE CONCAT('%', #{keyword}, '%')
        ORDER BY 
        CASE 
          WHEN food_name = #{keyword} THEN 1 
          WHEN food_name LIKE CONCAT(#{keyword}, '%') THEN 2 
          WHEN food_name LIKE CONCAT('%', #{keyword}) THEN 3 
          ELSE 4 
        END, 
        LENGTH(food_name) ASC 
        LIMIT #{limit}
    </select>

    <!-- 获取所有食物分类 -->
    <select id="getAllCategories" resultType="string">
        SELECT DISTINCT SUBSTRING(food_code, 1, 2) as category_code
        FROM china_foods
        WHERE food_code IS NOT NULL AND food_code != '' AND LENGTH(food_code) >= 2
        ORDER BY category_code
    </select>

    <!-- 根据分类获取食物列表 -->
    <select id="getFoodsByCategory" resultMap="ChinaFoodResultMap">
        SELECT * FROM china_foods 
        WHERE food_code LIKE CONCAT(#{categoryCode}, '%') 
        ORDER BY food_name 
        LIMIT #{limit}
    </select>

    <!-- 获取热门食物 -->
    <select id="getPopularFoods" resultMap="ChinaFoodResultMap">
        SELECT * FROM china_foods
        WHERE energy_kcal IS NOT NULL
        AND food_name NOT LIKE '%，%'
        AND food_name NOT LIKE '%（%'
        AND food_code NOT LIKE '07%'  <!-- 排除油脂类食物 -->
        ORDER BY
        CASE
          <!-- {{ AURA-X: Modify - 扩大优先食物列表，增加更多常见食物. Approval: 寸止. }} -->
          WHEN food_name IN ('大米', '小麦粉', '鸡蛋', '牛奶', '猪肉', '鸡肉', '苹果', '香蕉', '白菜', '土豆',
                             '面条', '馒头', '牛肉', '鱼肉', '豆腐', '西红柿', '黄瓜', '胡萝卜', '菠菜', '橙子',
                             '米饭', '面包', '酸奶', '瘦肉', '青菜', '玉米', '红薯', '花生', '核桃', '芝麻') THEN 1
          <!-- 按食物分类给予不同优先级，实现分类平衡 -->
          WHEN food_code LIKE '01%' THEN 2  <!-- 谷类 -->
          WHEN food_code LIKE '02%' THEN 2  <!-- 薯类 -->
          WHEN food_code LIKE '03%' THEN 2  <!-- 豆类 -->
          WHEN food_code LIKE '04%' THEN 3  <!-- 蔬菜类 -->
          WHEN food_code LIKE '05%' THEN 3  <!-- 水果类 -->
          WHEN food_code LIKE '08%' THEN 2  <!-- 肉类 -->
          WHEN food_code LIKE '09%' THEN 2  <!-- 蛋类 -->
          WHEN food_code LIKE '10%' THEN 2  <!-- 奶类 -->
          ELSE 4
        END,
        <!-- {{ AURA-X: Modify - 调整排序逻辑，平衡营养价值和多样性. Approval: 寸止. }} -->
        CASE
          WHEN energy_kcal BETWEEN 100 AND 400 THEN 1  <!-- 优先中等热量食物 -->
          WHEN energy_kcal BETWEEN 50 AND 600 THEN 2   <!-- 其次是适中范围 -->
          ELSE 3
        END,
        RAND()  <!-- 增加随机性，避免总是相同结果 -->
        LIMIT #{limit}
    </select>

    <!-- 根据ID获取食物详情 -->
    <select id="getFoodById" resultMap="ChinaFoodResultMap">
        SELECT * FROM china_foods WHERE id = #{id}
    </select>

</mapper>
